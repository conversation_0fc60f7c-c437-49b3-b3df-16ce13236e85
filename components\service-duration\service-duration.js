Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    serviceDurationRecords: {
      type: Array,
      value: []
    },
    allAdditionalServices: {
      type: Array,
      value: []
    },
    serviceDurationStatistics: {
      type: Object,
      value: {}
    },
    showServiceDuration: {
      type: Boolean,
      value: true
    }
  },

  data: {},

  methods: {
    // 切换服务时长统计显示
    onToggleServiceDuration() {
      this.triggerEvent('toggleServiceDuration');
    },

    // 开始主服务时长统计
    onStartMainService(e) {
      const { orderDetailId, serviceId, serviceName } = e.currentTarget.dataset;
      this.triggerEvent('startMainService', {
        orderDetailId,
        serviceId,
        serviceName
      });
    },

    // 开始增项服务时长统计
    onStartAdditionalService(e) {
      const { serviceType, serviceId, additionalServiceId, orderDetailId, serviceName } = e.currentTarget.dataset;
      this.triggerEvent('startAdditionalService', {
        serviceType,
        serviceId,
        additionalServiceId,
        orderDetailId,
        serviceName
      });
    },

    // 结束服务时长统计
    onEndServiceDuration(e) {
      const { recordId, serviceName } = e.currentTarget.dataset;
      this.triggerEvent('endServiceDuration', {
        recordId,
        serviceName
      });
    }
  }
});
