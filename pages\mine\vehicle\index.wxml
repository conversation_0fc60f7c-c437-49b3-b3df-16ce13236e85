<view class="container">

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 车辆信息 -->
  <view wx:else class="vehicle-container">
    <!-- 车辆基本信息 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-left">
          <text class="card-icon">🚗</text>
          <text class="card-title">车辆基本信息</text>
        </view>
      </view>

      <view class="vehicle-summary">
        <view class="plate-number">
          <text class="plate-text">{{vehicleInfo.plateNumber}}</text>
        </view>
        <view class="vehicle-meta">
          <view class="meta-item">
            <text class="meta-icon">🏷️</text>
            <text class="meta-text">{{vehicleInfo.vehicleTypeName || vehicleInfo.vehicleType}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">📊</text>
            <text class="meta-text status-{{vehicleInfo.statusClass}}">{{vehicleInfo.status}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 车辆详细信息 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-left">
          <text class="card-icon">📋</text>
          <text class="card-title">车辆详细信息</text>
        </view>
        <view class="card-actions">
          <button wx:if="{{!editing}}" class="btn-edit" catchtap="toggleEdit">
            <text class="btn-icon">✏️</text>
            <text>编辑</text>
          </button>
          <view wx:else class="edit-actions">
            <button class="btn-cancel" catchtap="toggleEdit">
              <text class="btn-icon">❌</text>
              <text>取消</text>
            </button>
            <button class="btn-save" catchtap="saveVehicleInfo">
              <text class="btn-icon">💾</text>
              <text>保存</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 里程数 -->
      <view class="form-item">
        <view class="item-header">
          <text class="item-title">里程数</text>
          <text class="item-unit">（公里）</text>
        </view>
        <view class="item-content">
          <input wx:if="{{editing}}"
                 class="form-input"
                 type="digit"
                 placeholder="请输入里程数"
                 value="{{formData.mileage}}"
                 data-field="mileage"
                 bindinput="onInputChange" />
          <text wx:else class="item-value">{{vehicleInfo.mileage ? vehicleInfo.mileage + ' 公里' : '未填写'}}</text>
        </view>
      </view>

      <!-- 外观描述 -->
      <view class="form-item">
        <view class="item-header">
          <text class="item-title">外观描述</text>
        </view>
        <view class="item-content">
          <textarea wx:if="{{editing}}"
                    class="form-textarea"
                    placeholder="请描述车辆外观状况"
                    value="{{formData.appearance}}"
                    data-field="appearance"
                    bindinput="onInputChange"
                    maxlength="1000" />
          <text wx:else class="item-value">{{vehicleInfo.appearance || '未填写'}}</text>
        </view>
      </view>

      <!-- 保险到期时间 -->
      <view class="form-item">
        <view class="item-header">
          <text class="item-title">保险到期时间</text>
        </view>
        <view class="item-content">
          <picker wx:if="{{editing}}"
                  mode="date"
                  value="{{formData.insuranceExpiry}}"
                  start="{{minDate}}"
                  end="{{maxDate}}"
                  bindchange="onInsuranceDateChange">
            <view class="date-picker">
              <text class="date-text {{formData.insuranceExpiry ? '' : 'placeholder'}}">{{formData.insuranceExpiry || '请选择日期'}}</text>
              <text class="icon-arrow">></text>
            </view>
          </picker>
          <text wx:else class="item-value">{{vehicleInfo.insuranceExpiry ? formData.insuranceExpiry : '未填写'}}</text>
        </view>
      </view>

      <!-- 行驶证到期时间 -->
      <view class="form-item">
        <view class="item-header">
          <text class="item-title">行驶证到期时间</text>
        </view>
        <view class="item-content">
          <picker wx:if="{{editing}}"
                  mode="date"
                  value="{{formData.licenseExpiry}}"
                  start="{{minDate}}"
                  end="{{maxDate}}"
                  bindchange="onLicenseDateChange">
            <view class="date-picker">
              <text class="date-text {{formData.licenseExpiry ? '' : 'placeholder'}}">{{formData.licenseExpiry || '请选择日期'}}</text>
              <text class="icon-arrow">></text>
            </view>
          </picker>
          <text wx:else class="item-value">{{vehicleInfo.licenseExpiry ? formData.licenseExpiry : '未填写'}}</text>
        </view>
      </view>

      <!-- 物资清单 -->
      <view class="form-item">
        <view class="item-header">
          <text class="item-title">物资清单</text>
        </view>
        <view class="item-content">
          <textarea wx:if="{{editing}}"
                    class="form-textarea"
                    placeholder="请列出车辆配备的物资清单"
                    value="{{formData.supplies}}"
                    data-field="supplies"
                    bindinput="onInputChange"
                    maxlength="2000" />
          <text wx:else class="item-value">{{vehicleInfo.supplies || '未填写'}}</text>
        </view>
      </view>
    </view>

    <!-- 更新记录 -->
    <view wx:if="{{vehicleInfo.lastSubmittedAt}}" class="info-card update-record">
      <view class="card-header">
        <view class="header-left">
          <text class="card-icon">🕒</text>
          <text class="card-title">最后更新记录</text>
        </view>
      </view>
      <view class="update-info">
        <view class="update-item">
          <text class="update-icon">📅</text>
          <view class="update-content">
            <text class="update-label">更新时间</text>
            <text class="update-value">{{vehicleInfo.lastSubmittedAt}}</text>
          </view>
        </view>
        <view wx:if="{{vehicleInfo.lastSubmittedEmployee}}" class="update-item">
          <text class="update-icon">👤</text>
          <view class="update-content">
            <text class="update-label">更新人</text>
            <text class="update-value">{{vehicleInfo.lastSubmittedEmployee.name}} ({{vehicleInfo.lastSubmittedEmployee.phone}})</text>
          </view>
        </view>
      </view>
    </view>
  </view>


</view>
