# 服务时长统计功能说明

## 功能概述

服务时长统计模块用于记录和统计每个服务项目和增项服务的具体用时，支持独立的服务计时和智能的整体服务管理，帮助员工和管理层了解服务效率，优化服务流程。

## 核心设计理念

### 1. 分层计时体系
- **整体服务时长**：从开始服务到完成服务的总时间跨度
- **主服务时长**：每个主服务项目的独立计时
- **增项服务时长**：每个增项服务的独立计时
- **智能联动**：各层级之间的自动关联和状态同步

### 2. 用户体验优化
- **自动开始**：整体开始服务时，主服务自动开始计时
- **独立控制**：每个服务都有独立的开始/结束按钮
- **智能提醒**：所有服务完成时自动提醒完成整体服务
- **实时更新**：服务时长每分钟自动更新显示

## 主要功能

### 1. 智能计时系统
- 整体开始服务时，主服务自动开始计时
- 支持主服务和增项服务的独立计时
- 实时显示当前服务用时
- 自动检测所有服务完成状态

### 2. 服务时长记录
- 记录每个服务的开始时间和结束时间
- 自动计算服务时长
- 支持添加备注信息
- 区分主服务和增项服务记录

### 3. 统计查看与管理
- 查看当前进行中的服务
- 查看历史服务时长记录
- 按订单查看所有服务时长
- 服务完成度统计和汇总

## 使用流程

### 员工端操作

#### 1. 整体服务开始
1. 在订单列表页面点击"开始服务"
2. 系统自动开始整体服务和主服务时长统计
3. 可选择直接开始或上传服务前照片
4. 主服务自动进入"进行中"状态

#### 2. 独立服务管理
1. 进入订单详情页面的"服务时长统计"模块
2. 查看主服务和增项服务的计时状态
3. 可以独立完成主服务（点击"完成"按钮）
4. 可以独立开始增项服务（点击"开始"按钮）
5. 可以独立完成增项服务（点击"完成"按钮）

#### 3. 智能服务完成
1. 当所有主服务和增项服务都完成时
2. 系统自动弹出提醒："是否完成整体服务？"
3. 确认后自动完成整体服务并返回订单列表
4. 或者可以选择"稍后完成"继续其他操作

#### 4. 查看服务进度
1. 进入"我的"页面 → "服务时长"菜单
2. 查看当前进行中的所有服务列表
3. 实时显示已用时长和服务状态
4. 可以直接在此页面结束任何进行中的服务

#### 5. 查看历史记录
1. 在订单详情页面查看"服务时长统计"模块
2. 查看主服务和增项服务的详细记录
3. 显示服务完成度统计和总用时
4. 支持展开/折叠详细信息

## 页面说明

### 1. 服务时长统计页面 (`/pages/serviceDuration/index`)
- **功能**: 显示当前进行中的服务列表
- **特点**: 
  - 实时更新服务时长
  - 支持下拉刷新
  - 可直接结束计时
  - 点击卡片查看订单详情

### 2. 订单详情页面服务时长模块
- **功能**: 显示当前订单的所有服务时长记录
- **特点**:
  - 可折叠展开
  - 区分进行中和已完成的服务
  - 支持结束当前服务计时

## API接口

### 1. 开始服务时长统计
```
POST /employee/service-duration/start
```

### 2. 结束服务时长统计
```
POST /employee/service-duration/end
```

### 3. 查询订单服务时长记录
```
GET /employee/service-duration/records/{orderId}
```

### 4. 获取当前进行中的服务
```
GET /employee/service-duration/current
```

## 数据结构

### 服务时长记录
```javascript
{
  id: 1,                              // 记录ID
  orderId: 123,                       // 订单ID
  orderDetailId: 456,                 // 订单详情ID（主服务）
  additionalServiceOrderId: 789,      // 增项服务订单ID（增项服务）
  employeeId: 1,                      // 员工ID
  recordType: "main_service",         // 记录类型
  serviceId: 10,                      // 服务ID
  serviceName: "基础洗护",            // 服务名称
  startTime: "2024-12-30T10:00:00Z",  // 开始时间
  endTime: "2024-12-30T11:30:00Z",    // 结束时间
  duration: 90,                       // 时长（分钟）
  remark: "服务完成"                  // 备注
}
```

## 注意事项

1. **自动计时**: 开始服务时会自动开始计时，无需手动操作
2. **实时更新**: 服务时长每分钟自动更新一次
3. **数据准确性**: 确保网络连接稳定，避免计时数据丢失
4. **及时结束**: 服务完成后请及时点击"结束计时"
5. **多服务支持**: 支持同时进行多个服务的计时

## 样式特点

- **现代化设计**: 采用渐变色和圆角设计
- **清晰层次**: 不同状态的服务用不同颜色区分
- **响应式布局**: 适配不同屏幕尺寸
- **交互友好**: 点击反馈和动画效果

## 技术实现

- **前端**: 微信小程序原生开发
- **状态管理**: 页面级状态管理
- **定时器**: 使用setInterval实现实时更新
- **API调用**: 基于Promise的异步请求
- **错误处理**: 完善的错误提示和处理机制
