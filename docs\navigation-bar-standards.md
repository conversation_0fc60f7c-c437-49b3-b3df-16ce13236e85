# 导航栏统一标准

## 标准配置

根据用户协议页面的标准，所有子页面的导航栏配置应统一为：

```json
{
  "navigationStyle": "default",
  "navigationBarTitleText": "页面标题",
  "navigationBarTextStyle": "black",
  "navigationBarBackgroundColor": "#fff"
}
```

## 已统一的页面

### 1. 车辆管理相关页面
- ✅ `pages/mine/vehicle/index.json` - 车辆管理

### 2. 推广相关页面
- ✅ `pages/mine/promotion/index.json` - 我的推广
- ✅ `pages/mine/promotion/customers/index.json` - 推广用户

### 3. 打卡相关页面
- ✅ `pages/mine/checkin/index.json` - 出车拍照
- ✅ `pages/mine/checkin/history/index.json` - 打卡历史
- ✅ `pages/mine/checkin/statistics/index.json` - 打卡统计

### 4. 服务时长页面
- ✅ `pages/serviceDuration/index.json` - 服务时长统计

### 5. 协议页面（标准参考）
- ✅ `pages/mine/userAgreement/index.json` - 用户协议
- ✅ `pages/mine/privacyAgreement/index.json` - 隐私协议

## 返回逻辑

### 系统默认返回
使用 `navigationStyle: "default"` 时，微信小程序会自动提供：
- 左上角返回按钮
- 自动执行 `wx.navigateBack()` 返回上一页
- 无需额外的JavaScript代码处理返回逻辑

### 特殊返回处理
如果需要特殊的返回逻辑，可以在页面JS中重写：

```javascript
// 监听页面返回事件（仅在特殊情况下使用）
onUnload() {
  // 页面卸载前的处理
  console.log('页面卸载');
},

// 如果需要拦截返回（不推荐）
onBackPress() {
  // 返回 true 阻止默认返回行为
  // 返回 false 或不返回值允许正常返回
  return false;
}
```

## 样式特点

### 导航栏样式
- **背景色**: 白色 (`#fff`)
- **文字色**: 黑色 (`black`)
- **返回按钮**: 系统默认样式
- **标题**: 居中显示

### 与自定义导航栏的区别
- **自定义导航栏**: 需要手动处理返回逻辑、适配不同设备
- **系统导航栏**: 自动适配、统一体验、无需额外代码

## 迁移说明

### 从自定义导航栏迁移
1. **修改JSON配置**: 将 `navigationStyle: "custom"` 改为 `"default"`
2. **移除自定义组件**: 删除 `diy-navbar` 组件引用
3. **清理WXML**: 移除自定义导航栏相关代码
4. **清理WXSS**: 移除导航栏相关样式
5. **清理JS**: 移除返回按钮事件处理代码

### 示例对比

**修改前（自定义导航栏）**:
```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "diy-navbar": "/components/diy-navbar/diy-navbar"
  }
}
```

**修改后（系统导航栏）**:
```json
{
  "navigationStyle": "default",
  "navigationBarTitleText": "页面标题",
  "navigationBarTextStyle": "black",
  "navigationBarBackgroundColor": "#fff"
}
```

## 优势

1. **一致性**: 所有页面导航栏样式统一
2. **维护性**: 减少自定义代码，降低维护成本
3. **兼容性**: 系统导航栏自动适配不同设备和系统版本
4. **用户体验**: 符合微信小程序标准交互模式
5. **开发效率**: 无需处理复杂的导航栏逻辑

## 注意事项

1. **特殊页面**: 首页、订单列表等主要页面可能需要保持自定义导航栏
2. **功能页面**: 所有功能子页面建议使用系统导航栏
3. **一致性检查**: 定期检查新增页面是否遵循统一标准
