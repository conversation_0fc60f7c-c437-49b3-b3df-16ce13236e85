// pages/mine/checkin/index.js
import checkinApi from '../../../api/modules/employee-checkin.js';
import Session from '../../../common/Session.js';
import AddressUtils from '../../../utils/AddressUtils.js';
import { formatNormalDate } from '../../utils/util';

Page({
  data: {
    userInfo: {},
    photos: {
      vehicleExterior: [], // 车辆外观照片，最多9张
      serviceStaff: [], // 服务人员照片，最多9张
      vehicleInterior: [], // 车内情况照片，最多9张
    },
    description: '', // 打卡描述
    location: null, // 位置信息
    uploading: false, // 上传状态
    todayCheckins: [], // 今日打卡记录
    statistics: {}, // 统计信息
    showLocationPicker: false, // 是否显示位置选择
    canSubmit: false, // 是否可以提交
    activePhotoType: 'vehicleExterior', // 当前选择的照片类型
    isForceMode: false, // 是否为强制打卡模式
  },

  onLoad(options) {
    // 检查是否为强制打卡模式
    const isForceMode = options && options.force === 'true';
    this.setData({ isForceMode });

    this.initUserInfo();
    this.loadTodayCheckins();
    this.loadStatistics();
  },

  onShow() {
    // 每次显示页面时刷新今日记录
    this.loadTodayCheckins();

    // 强制打卡模式下，禁用返回按钮
    if (this.data.isForceMode) {
      wx.hideHomeButton();
      // 监听返回事件
      this.setupBackButtonIntercept();
    }
  },

  // 设置返回按钮拦截
  setupBackButtonIntercept() {
    const that = this;
    // 重写页面的 onBackPress 方法（如果支持的话）
    if (typeof this.onBackPress === 'undefined') {
      this.onBackPress = function () {
        if (that.data.isForceMode) {
          wx.showModal({
            title: '提示',
            content: '请完成出车打卡后再离开',
            showCancel: false,
          });
          return true; // 阻止默认返回行为
        }
        return false; // 允许返回
      };
    }
  },

  // 页面卸载前的处理
  onUnload() {
    // 强制打卡模式下的提示已通过其他方式处理
    // onUnload 无法阻止页面卸载，这里只做清理工作
    console.log('打卡页面卸载');
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.redirectTo({
        url: '/pages/login/index',
      });
      return;
    }
    this.setData({ userInfo });
  },

  // 加载今日打卡记录
  async loadTodayCheckins() {
    try {
      const result = await checkinApi.getTodayList(this.data.userInfo.id);
      if (result && result.list) {
        // 格式化时间和地址显示
        const formattedList = [];
        for (const item of result.list) {
          const address = await AddressUtils.getAddressFromLocation(item.latitude, item.longitude);
          item.formatTime = formatNormalDate(item.checkInTime);
          item.displayAddress = AddressUtils.formatAddressDisplay({ ...item, address });
          formattedList.push(item);
        }

        this.setData({
          todayCheckins: formattedList,
        });

        // 批量解析地址信息
        this.resolveAddressesForCheckins(formattedList);
      }
    } catch (error) {
      console.error('加载今日打卡记录失败:', error);
    }
  },

  // 批量解析打卡记录的地址信息
  async resolveAddressesForCheckins(checkinList) {
    try {
      await AddressUtils.batchResolveAddresses(checkinList, (index, address) => {
        // 更新指定索引的记录地址
        const updatedList = [...this.data.todayCheckins];
        if (updatedList[index]) {
          updatedList[index].displayAddress = address;
          this.setData({
            todayCheckins: updatedList,
          });
        }
      });
    } catch (error) {
      console.error('批量解析地址失败:', error);
    }
  },

  // 加载统计信息
  async loadStatistics() {
    try {
      // 使用简化的统计API获取基础数据
      const result = await checkinApi.getStatistics(this.data.userInfo.id);
      if (result) {
        this.setData({
          statistics: {
            todayCount: result.todayCount || 0,
            totalCount: result.totalCount || 0,
            employeeCount: result.employeeCount || 0,
          },
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  },

  // 切换照片类型
  switchPhotoType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      activePhotoType: type,
    });
  },

  // 选择照片
  chooseImage() {
    const { photos, activePhotoType } = this.data;
    const currentPhotos = photos[activePhotoType];
    const remainCount = 9 - currentPhotos.length;

    if (remainCount <= 0) {
      wx.showToast({
        title: '最多只能上传9张照片',
        icon: 'none',
      });
      return;
    }

    this.setData({ uploading: true });

    const keyPrefix = `employee-checkin/${this.data.userInfo.id}/${Date.now()}/`;

    this.uploadImage(
      this,
      '', // 存储字段
      keyPrefix, // 上传key前缀
      remainCount // 最大数量
    )
      .then(res => {
        // 将新上传的照片与已有照片合并
        const { photos, activePhotoType } = this.data;
        const newPhotos = { ...photos };
        newPhotos[activePhotoType] = [...photos[activePhotoType], ...res];

        this.setData({
          photos: newPhotos,
          uploading: false,
        });

        // 检查提交状态
        this.checkCanSubmit();

        wx.showToast({
          title: `上传成功，共${newPhotos[activePhotoType].length}张照片`,
          icon: 'success',
        });
      })
      .catch(error => {
        console.error('上传失败:', error);
        this.setData({ uploading: false });
        wx.showToast({
          title: '上传失败',
          icon: 'none',
        });
      });
  },

  // 删除照片
  deletePhoto(e) {
    const { index, type } = e.currentTarget.dataset;
    const { photos } = this.data;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: res => {
        if (res.confirm) {
          const newPhotos = { ...photos };
          newPhotos[type].splice(index, 1);
          this.setData({ photos: newPhotos });
          // 检查提交状态
          this.checkCanSubmit();
        }
      },
    });
  },

  // 预览照片
  previewImage(e) {
    const { url, type } = e.currentTarget.dataset;
    const { photos } = this.data;

    // 如果指定了类型，只预览该类型的照片，否则预览所有照片
    let urls = [];
    if (type) {
      urls = photos[type];
    } else {
      // 合并所有类型的照片
      urls = [...photos.vehicleExterior, ...photos.serviceStaff, ...photos.vehicleInterior];
    }

    wx.previewImage({
      current: url,
      urls: urls,
    });
  },

  // 输入描述
  onDescriptionInput(e) {
    const description = e.detail.value;
    this.setData({
      description,
    });
    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { photos, description } = this.data;
    const totalPhotos = photos.vehicleExterior.length + photos.serviceStaff.length + photos.vehicleInterior.length;
    const canSubmit = totalPhotos > 0 && description.trim().length > 0;
    this.setData({ canSubmit });
  },

  // 获取位置
  getLocation() {
    wx.showModal({
      title: '位置信息',
      content: '是否要记录当前位置信息？',
      success: res => {
        if (res.confirm) {
          this.getCurrentLocation();
        }
      },
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.showLoading({ title: '获取位置中...' });

    // 先检查位置权限
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          this.getLocationData();
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.getLocationData();
            },
            fail: () => {
              wx.hideLoading();
              wx.showModal({
                title: '位置权限',
                content: '需要获取您的位置信息来记录打卡地点，请在设置中开启位置权限',
                confirmText: '去设置',
                success: modalRes => {
                  if (modalRes.confirm) {
                    wx.openSetting();
                  }
                },
              });
            },
          });
        }
      },
    });
  },

  // 获取位置数据
  getLocationData() {
    wx.getLocation({
      type: 'gcj02',
      success: res => {
        const { latitude, longitude } = res;

        // 使用腾讯地图逆地理编码获取地址
        this.reverseGeocode(latitude, longitude);
      },
      fail: error => {
        wx.hideLoading();
        console.error('获取位置失败:', error);
        wx.showToast({
          title: '位置获取失败，请检查定位服务是否开启',
          icon: 'none',
        });
      },
    });
  },

  // 逆地理编码获取地址
  async reverseGeocode(latitude, longitude) {
    try {
      const address = await AddressUtils.getAddressFromLocation(latitude, longitude, { showLoading: true });

      this.setData({
        location: {
          latitude,
          longitude,
          address: address || '位置获取失败',
        },
      });

      wx.showToast({
        title: '位置获取成功',
        icon: 'success',
      });
    } catch (error) {
      console.error('地址解析失败:', error);

      // 地址解析失败时，仍然保存经纬度
      this.setData({
        location: {
          latitude,
          longitude,
          address: '位置获取失败',
        },
      });

      wx.showToast({
        title: '位置获取成功',
        icon: 'success',
      });
    }
  },

  // 清除位置
  clearLocation() {
    this.setData({ location: null });
  },

  // 提交打卡
  async submitCheckin() {
    const { photos, description, location, userInfo } = this.data;

    console.log('当前用户信息:', userInfo);
    const token = Session.getToken();
    console.log('当前token:', token);
    console.log('token长度:', token ? token.length : 0);
    console.log('token包含非ASCII字符:', token ? /[^\x00-\x7F]/.test(token) : false);

    // 检查是否至少有一张照片
    const totalPhotos = photos.vehicleExterior.length + photos.serviceStaff.length + photos.vehicleInterior.length;
    if (totalPhotos === 0) {
      wx.showToast({
        title: '请至少上传一张照片',
        icon: 'none',
      });
      return;
    }

    if (description.trim().length === 0) {
      wx.showToast({
        title: '请输入打卡描述',
        icon: 'none',
      });
      return;
    }

    if (description.length > 500) {
      wx.showToast({
        title: '描述不能超过500个字符',
        icon: 'none',
      });
      return;
    }

    // 确保数据格式正确，使用新的分组照片结构
    const checkinData = {
      employeeId: parseInt(userInfo.id),
      photos: {
        vehicleExterior: photos.vehicleExterior || [],
        serviceStaff: photos.serviceStaff || [],
        vehicleInterior: photos.vehicleInterior || [],
      },
      description: description.trim(),
    };

    // 添加位置信息（如果有）
    if (location) {
      checkinData.latitude = parseFloat(location.latitude);
      checkinData.longitude = parseFloat(location.longitude);
      checkinData.address = location.address;
    }

    console.log('提交打卡数据:', checkinData);
    console.log('数据JSON字符串:', JSON.stringify(checkinData));

    try {
      const result = await checkinApi.create(checkinData);

      // analysisRes 函数会在 errCode !== 0 时返回 null 并显示错误信息
      // 所以 result 不为 null 就表示成功
      if (result !== null) {
        wx.showToast({
          title: '打卡成功',
          icon: 'success',
        });

        // 重置表单
        this.setData({
          photos: {
            vehicleExterior: [],
            serviceStaff: [],
            vehicleInterior: [],
          },
          description: '',
          location: null,
          canSubmit: false,
        });

        // 刷新今日记录和统计
        this.loadTodayCheckins();
        this.loadStatistics();

        // 如果是强制打卡模式，打卡成功后返回首页
        if (this.data.isForceMode) {
          // 设置一个标记，表示强制打卡已完成
          wx.setStorageSync('forceCheckinCompleted', true);

          // 立即更新强制模式状态，避免离开页面时的提示
          this.setData({
            isForceMode: false,
          });

          setTimeout(() => {
            wx.navigateBack({
              delta: 1,
            });
          }, 1500);
        }
      }
      // 如果 result 为 null，说明 analysisRes 已经处理了错误并显示了错误信息
    } catch (error) {
      console.error('打卡失败:', error);
      // 处理网络错误或其他异常
      let errorMsg = '网络错误，请检查网络连接后重试';

      if (error && error.errMsg) {
        // 微信小程序的错误格式
        errorMsg = error.errMsg;
      } else if (error && error.message) {
        errorMsg = error.message;
      } else if (typeof error === 'string') {
        errorMsg = error;
      }

      wx.showToast({
        title: errorMsg,
        icon: 'none',
      });
    }
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/mine/checkin/history/index',
    });
  },
});
