// src/utils/utils.js
function formatDate(timeStamp) {
  const date = new Date(timeStamp);

  return `${date.getMonth() + 1}月${date.getDate()}日 ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
}
/**
 * 数据库返回时间的日常格式化，默认返回时分秒
 * @param {*} date
 * @param {*} showTime
 */
function formatNormalDate(date, showTime = true) {
  if (!date) return '';
  if (typeof date === 'string') {
    date = new Date(date);
  } else if (typeof date === 'number') {
    date = new Date(date * 1000); // 如果是时间戳，转换为毫秒
  } else if (!(date instanceof Date)) {
    console.error('Invalid date format:', date);
    return '';
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${showTime ? `${hours}:${minutes}:${seconds}` : ''}`;
}

function removeValue(array, valueToRemove) {
  let index = array.indexOf(valueToRemove);
  while (index !== -1) {
    array.splice(index, 1); // 删除指定索引的元素
    index = array.indexOf(valueToRemove); // 查找下一个指定值的索引
  }
  return array; // 返回修改后的原数组
}

function padZero(num) {
  return num < 10 ? '0' + num : num;
}

function generateId() {
  const timestamp = Date.now().toString(36); // 时间戳转36进制
  const random = Math.random().toString(36).substr(2, 5); // 随机数
  return `${timestamp}-${random}`;
}

function formatDateTime(date, format = 'YYYY-MM-DD') {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const pad = n => (n < 10 ? `0${n}` : n);

  const replacements = {
    YYYY: d.getFullYear(),
    MM: pad(d.getMonth() + 1),
    DD: pad(d.getDate()),
    HH: pad(d.getHours()),
    mm: pad(d.getMinutes()),
    ss: pad(d.getSeconds()),
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => replacements[match]);
}

export { formatDate, generateId, padZero, formatNormalDate, removeValue, formatDateTime };

export default {
  formatDate,
  generateId,
  padZero,
  formatNormalDate,
  removeValue,
  formatDateTime,
};
