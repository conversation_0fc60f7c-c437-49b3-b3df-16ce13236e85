.bottom-action-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, rgba(255, 255, 255, 1) 100%);
  padding: 20rpx 40rpx 40rpx;
  z-index: 100;
  backdrop-filter: blur(10rpx);
}

.action-buttons-row {
  display: flex;
  gap: 20rpx;
  align-items: center;
  justify-content: center;
}

.single-action-btn {
  width: 100%;
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.single-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.single-action-btn.contact-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff4081);
  color: white;
}

.single-action-btn.review-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

.btn-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  width: 36rpx;
  height: 36rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
