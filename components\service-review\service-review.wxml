<!-- 服务评价区域 -->
<view class="review-section" wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}">
  <!-- 有评价时显示评价内容 -->
  <view class="review-content" wx:if="{{hasReview && reviewData && !reviewLoading}}">
    <view class="review-header">
      <view class="rating-section">
        <text class="rating-label">服务评分：</text>
        <text class="rating-stars">{{reviewData.ratingStars}}</text>
        <text class="rating-score">({{reviewData.rating}}分)</text>
      </view>
      <text class="review-time">{{reviewData.createdAt}}</text>
    </view>

    <!-- 评价内容 -->
    <view class="review-text" wx:if="{{reviewData.content}}">
      <text class="review-content-text">{{reviewData.content}}</text>
    </view>

    <!-- 评价图片 -->
    <view class="review-images" wx:if="{{reviewData.images && reviewData.images.length > 0}}">
      <view class="images-title">评价图片：</view>
      <view class="images-grid">
        <image
          wx:for="{{reviewData.images}}"
          wx:key="index"
          src="{{item}}"
          class="review-image"
          mode="aspectFill"
          bindtap="previewImage"
          data-current="{{item}}"
          data-urls="{{reviewData.images}}"
        ></image>
      </view>
    </view>
  </view>
</view>
