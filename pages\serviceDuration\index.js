import serviceDurationApi from '../../api/modules/serviceDuration';
import orderApi from '../../api/modules/order';
import { formatNormalDate } from '../utils/util';
import Session from '../../common/Session';

Page({
  data: {
    userInfo: null,
    currentServices: [], // 当前进行中的服务
    availableOrders: [], // 可以开始服务的订单列表
    loading: false,
    refreshing: false,
    timer: null, // 定时器
    // 结束服务弹框相关
    showEndServiceModal: false,
    endServiceModalTitle: '',
    endServiceModalContent: '',
    currentEndServiceData: null, // 当前要结束的服务数据
  },

  onLoad() {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadData();
    this.startTimer();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData();
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 加载所有数据
  async loadData() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 并行加载当前进行中的服务和可用订单
      const [currentResult, ordersResult] = await Promise.all([
        serviceDurationApi.getCurrent(),
        this.loadAvailableOrders()
      ]);

      // 处理当前进行中的服务
      const services = currentResult?.currentServices || [];
      if (services && Array.isArray(services)) {
        const formattedServices = services.map(service => ({
          ...service,
          startTime: service.startTime ? formatNormalDate(service.startTime) : null,
          startTimeObj: service.startTime ? new Date(service.startTime) : null,
          currentDuration: this.calculateCurrentDuration(service.startTime),
          currentDurationText: this.formatDuration(this.calculateCurrentDuration(service.startTime)),
          // 确保所有关联对象都正确处理
          order: service.order || {},
          service: service.service || {},
          orderDetail: service.orderDetail || {},
          additionalService: service.additionalService || {},
        }));

        this.setData({
          currentServices: formattedServices,
        });
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载可以开始服务的订单
  async loadAvailableOrders() {
    try {
      const { userInfo } = this.data;
      if (!userInfo || !userInfo.id) {
        console.error('用户信息不完整');
        return [];
      }

      // 获取员工名下服务中的订单
      const result = await orderApi.myList(userInfo.id, '服务中');
      const orders = result?.list || [];
      const availableOrders = [];

      // 为每个订单获取详细的服务状态
      for (const order of orders) {
        try {
          const serviceStatus = await serviceDurationApi.getOrderServiceStatus(order.id);
          if (serviceStatus) {
            // 处理主服务
            const mainServices = serviceStatus.mainServices || [];
            const additionalServices = serviceStatus.additionalServices || [];

            // 找出未开始的服务
            const unStartedMainServices = mainServices.filter(service => service.status === 'not_started');
            const unStartedAdditionalServices = additionalServices.filter(service => service.status === 'not_started');

            if (unStartedMainServices.length > 0 || unStartedAdditionalServices.length > 0) {
              availableOrders.push({
                ...order,
                mainServices: unStartedMainServices,
                additionalServices: unStartedAdditionalServices,
                serviceStatus: serviceStatus
              });
            }
          }
        } catch (error) {
          console.error(`获取订单${order.id}服务状态失败:`, error);
        }
      }

      this.setData({
        availableOrders: availableOrders
      });

      return availableOrders;
    } catch (error) {
      console.error('加载可用订单失败:', error);
      return [];
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true });
    await this.loadData();
    this.setData({ refreshing: false });
    wx.stopPullDownRefresh();
  },

  // 计算当前服务时长（分钟）
  calculateCurrentDuration(startTime) {
    if (!startTime) return 0;
    const start = new Date(startTime);
    const now = new Date();
    return Math.floor((now - start) / (1000 * 60));
  },

  // 格式化时长显示
  formatDuration(minutes) {
    if (!minutes || minutes < 0) return '0分钟';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}小时${mins}分钟`;
    }
    return `${mins}分钟`;
  },

  // 启动定时器，每分钟更新一次时长显示
  startTimer() {
    const timer = setInterval(() => {
      this.updateCurrentDuration();
    }, 60000); // 60秒更新一次

    this.setData({ timer });
  },

  // 更新当前服务时长显示
  updateCurrentDuration() {
    const { currentServices } = this.data;
    if (currentServices.length === 0) return;

    const updatedServices = currentServices.map(service => ({
      ...service,
      currentDuration: this.calculateCurrentDuration(service.startTime),
      currentDurationText: this.formatDuration(this.calculateCurrentDuration(service.startTime)),
    }));

    this.setData({
      currentServices: updatedServices,
    });
  },

  // 结束服务时长统计
  async endService(e) {
    const { recordId, serviceName } = e.currentTarget.dataset;

    // 获取当前服务的详细信息，用于显示更准确的提醒
    const currentService = this.data.currentServices.find(service => service.id == recordId);
    const orderId = currentService?.order?.id;

    let modalTitle = '确认结束服务';
    let modalContent = `结束后将自动完成该订单的所有主服务和增项服务，整个订单将标记为完成。`;

    // 保存当前要结束的服务数据
    this.setData({
      showEndServiceModal: true,
      endServiceModalTitle: modalTitle,
      endServiceModalContent: modalContent,
      currentEndServiceData: {
        recordId: recordId,
        serviceName: serviceName
      }
    });
  },

  // 确认结束服务
  async onConfirmEndService() {
    const { currentEndServiceData } = this.data;
    if (!currentEndServiceData) return;

    this.setData({
      showEndServiceModal: false
    });

    wx.showLoading({
      title: '处理中...',
    });

    try {
      const params = {
        recordId: currentEndServiceData.recordId,
        remark: `完成${currentEndServiceData.serviceName}服务`,
      };

      const result = await serviceDurationApi.end(params);
      if (result) {
        wx.showToast({
          title: '已结束计时',
          icon: 'success',
        });
        // 重新加载数据
        this.loadData();
      } else {
        wx.showToast({
          title: '操作失败',
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('结束服务时长统计失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 取消结束服务
  onCancelEndService() {
    this.setData({
      showEndServiceModal: false,
      currentEndServiceData: null
    });
  },



  // 查看订单详情
  viewOrderDetail(e) {
    const { orderId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index?orderId=${orderId}`,
    });
  },

  // 手动刷新
  refresh() {
    this.loadData();
  },
});
