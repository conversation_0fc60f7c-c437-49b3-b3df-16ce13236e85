/* pages/message/messageDetail/index.wxss */

.message-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 24rpx;
}

.content {
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 100rpx);
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.message-detail {
  padding: 32rpx;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

/* 未读状态的消息头部 */
.message-header.unread {
  background-color: #f8f9ff;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

/* 已读状态的消息头部 */
.message-header.read {
  opacity: 0.8;
}

.message-icon-wrapper {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

/* 未读红点 */
.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}

.message-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.message-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 未读标题样式 */
.unread-title {
  color: #333;
  font-weight: bold;
}

/* 已读标题样式 */
.read-title {
  color: #666;
  font-weight: normal;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

/* 已读状态标识 */
.read-status {
  font-size: 22rpx;
  color: #28a745;
  background-color: #d4edda;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.message-content {
  margin-bottom: 32rpx;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  word-wrap: break-word;
}

.message-extra {
  background-color: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.extra-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.extra-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.extra-list {
  margin-top: 12rpx;
}

.extra-item {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.extra-item:last-child {
  margin-bottom: 0;
}

.extra-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 120rpx;
  flex-shrink: 0;
}

.extra-value {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  word-wrap: break-word;
  flex: 1;
}