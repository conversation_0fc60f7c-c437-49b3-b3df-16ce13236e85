# 修改服务地址功能说明

## 功能概述

本功能实现了员工端修改订单服务地址的能力，支持通过多种方式选择和编辑地址信息。

## 功能特性

### 1. 权限控制
- **员工端权限**：只能在出发前修改地址（待付款、待接单、待服务状态）
- **管理端权限**：可在任何订单状态下修改服务地址（需要后续扩展）
- **用户端权限**：只能在出发前修改地址（需要后续扩展）

### 2. 地址编辑方式
- **手动输入**：支持输入服务地址、详细地址、地址备注
- **当前位置**：获取用户当前GPS位置并自动解析地址
- **地图选择**：通过微信地图选择具体位置
- **地址搜索**：输入地址后自动解析经纬度坐标

### 3. 功能入口
- **订单列表页面**：待服务状态订单卡片中的"修改地址"按钮
- **订单列表页面**：更多操作弹窗中的"更改服务地址"选项
- **订单详情页面**：服务地址旁边的"修改"按钮

## 技术实现

### 1. API接口
- **接口地址**：`PUT /orders/{orderId}/updateServiceAddress`
- **请求参数**：
  ```json
  {
    "address": "北京市朝阳区xxx街道",
    "addressDetail": "xxx小区1号楼101室", 
    "longitude": 116.123456,
    "latitude": 39.123456,
    "addressRemark": "门口有保安，请提前联系",
    "addressId": 1,
    "employeeId": 1,
    "userType": "employee"
  }
  ```

### 2. 组件结构
- **地址编辑器组件**：`/components/address-editor/`
  - `index.js`：组件逻辑
  - `index.wxml`：组件模板
  - `index.wxss`：组件样式
  - `index.json`：组件配置

### 3. 集成页面
- **订单列表页面**：`/pages/orders/index.*`
- **订单详情页面**：`/pages/orders/orderDetail/index.*`

## 使用流程

### 员工端操作流程
1. 在订单列表或订单详情页面找到待服务状态的订单
2. 点击"修改地址"或"更改服务地址"按钮
3. 在弹出的地址编辑器中：
   - 输入或修改服务地址信息
   - 选择获取当前位置或地图选择位置
   - 填写详细地址和备注信息
4. 点击"确认修改"提交更改
5. 系统验证权限和订单状态后更新地址信息
6. 页面自动刷新显示最新地址信息

## 注意事项

### 1. 权限限制
- 只有待付款、待接单、待服务状态的订单才能修改地址
- 已出发、服务中、已完成等状态的订单无法修改地址

### 2. 数据验证
- 详细地址为必填项
- 经纬度坐标会自动验证有效性范围
- 地址信息长度限制为255字符

### 3. 地图功能
- 使用高德地图SDK进行地址解析和位置选择
- 需要用户授权位置权限才能获取当前位置
- 地图选择功能依赖微信小程序的chooseLocation API

### 4. 错误处理
- 网络错误时会显示相应提示信息
- 权限不足时会提示"当前订单状态不允许修改地址"
- 位置获取失败时会提供手动输入选项

## 后续扩展

1. **管理端支持**：扩展管理端的地址修改权限和界面
2. **用户端支持**：为用户端添加地址修改功能
3. **地址历史**：记录地址修改历史和操作日志
4. **批量修改**：支持批量修改多个订单的服务地址
5. **地址验证**：增加地址有效性验证和服务范围检查
