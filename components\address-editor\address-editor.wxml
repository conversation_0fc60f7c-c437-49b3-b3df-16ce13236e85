<view class="address-editor {{show ? 'show' : ''}}" catchtap="onCancel">
  <view class="editor-content" catchtap="preventTap">
    <!-- 标题栏 -->
    <view class="editor-header">
      <text class="header-title">修改服务地址</text>
      <text class="close-btn" bindtap="onCancel">✕</text>
    </view>

    <!-- 地址表单 -->
    <view class="editor-body">
      <view class="location-section">
        <text class="section-title">选择位置</text>

        <view class="location-buttons">
          <button
            class="location-btn primary {{isGettingLocation ? 'loading' : ''}}"
            bindtap="getCurrentLocation"
            disabled="{{isGettingLocation}}"
          >
            {{isGettingLocation ? '获取中...' : '获取当前位置'}}
          </button>

          <button class="location-btn primary" bindtap="chooseLocationOnMap">
            地图选择
          </button>
        </view>
      </view>

      <!-- 地址输入 -->
      <view class="form-item">
        <text class="form-label">服务地址</text>
        <view class="address-input-row">
          <input
            class="form-input address-input"
            placeholder="请输入服务地址"
            value="{{address}}"
            bindinput="onAddressInput"
          />
          <!-- <button class="search-btn" bindtap="searchAddress">搜索</button> -->
        </view>
      </view>

      <!-- 详细地址输入 -->
      <view class="form-item">
        <text class="form-label required">详细地址</text>
        <input
          class="form-input"
          placeholder="请输入详细地址（如：xx小区x号楼x室）"
          value="{{addressDetail}}"
          bindinput="onAddressDetailInput"
          maxlength="255"
        />
        <text class="char-count">{{addressDetail.length}}/255</text>
      </view>

      <!-- 地址备注 -->
      <view class="form-item">
        <text class="form-label">地址备注</text>
        <input
          class="form-input"
          placeholder="请输入地址备注（如：门口有保安）"
          value="{{addressRemark}}"
          bindinput="onAddressRemarkInput"
          maxlength="255"
        />
        <text class="char-count">{{addressRemark.length}}/255</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="editor-footer">
      <button class="cancel-btn" bindtap="onCancel">取消</button>
      <button class="confirm-btn" bindtap="onConfirm">确认修改</button>
    </view>
  </view>
</view>
