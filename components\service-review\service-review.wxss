.review-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.review-loading {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.review-content {
  padding: 20rpx 0;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.rating-section {
  display: flex;
  align-items: center;
}

.rating-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.rating-stars {
  font-size: 32rpx;
  color: #ffb400;
  margin-right: 10rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #999;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

.review-text {
  margin-bottom: 20rpx;
}

.review-content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.review-images {
  margin-top: 20rpx;
}

.images-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.review-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.no-review {
  text-align: center;
  padding: 40rpx 0;
}

.no-review-text {
  font-size: 28rpx;
  color: #999;
}
