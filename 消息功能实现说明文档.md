# 微信小程序消息功能实现说明文档

## 目录
- [1. 功能概述](#1-功能概述)
- [2. 整体架构](#2-整体架构)
- [3. 核心功能模块](#3-核心功能模块)
- [4. 数据结构设计](#4-数据结构设计)
- [5. API接口设计](#5-api接口设计)
- [6. WebSocket实时通知](#6-websocket实时通知)
- [7. 样式设计](#7-样式设计)
- [8. 复用指南](#8-复用指南)
- [9. 技术要点](#9-技术要点)

## 1. 功能概述

本消息系统是一个完整的微信小程序消息功能实现，支持：
- 消息列表展示（已读/未读状态）
- 消息详情查看
- 实时消息推送（WebSocket）
- 消息已读标记
- 下拉刷新
- 空状态处理
- 消息分类（系统消息、平台消息、订单消息）

## 2. 整体架构

```
消息系统架构：
├── 前端页面层
│   ├── 消息列表页 (pages/message/index)
│   └── 消息详情页 (pages/message/messageDetail/index)
├── API接口层 (api/modules/message.js)
├── WebSocket通知层 (pages/index/index.js)
├── 工具类层 (pages/utils/util.js, common/Session.js)
└── 配置层 (api/config.js)
```

## 3. 核心功能模块

### 3.1 消息列表页面 (`pages/message/index.js`)

#### 主要功能
- 用户身份验证和重定向
- 消息列表加载和格式化
- 消息图标映射（system/platform/order类型）
- 点击消息跳转详情并标记已读
- 下拉刷新功能

#### 核心代码结构
```javascript
Page({
  data: {
    userInfo: null,
    newsList: [],
    loading: false,
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo) {
      return wx.redirectTo({
        url: "/pages/login/index",
      });
    }
    this.setData({ userInfo });
    this.loadMessageList();
  },

  // 加载消息列表
  async loadMessageList() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    try {
      const res = await messageApi.getMessageList(this.data.userInfo.id);
      const {list} = res || {};
      if (list && list.length >= 0) {
        const formattedList = list.map(item => ({
          ...item,
          time: utils.formatDate(new Date(item.createdAt)),
          icon: this.getMessageIcon(item.type)
        }));
        this.setData({ newsList: formattedList });
      }
    } catch (error) {
      console.error('获取消息列表失败:', error);
      wx.showToast({
        title: '获取消息失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 根据消息类型获取图标
  getMessageIcon(type) {
    const iconMap = {
      'system': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
      'platform': '//xian7.zos.ctyun.cn/pet/static/msg2.png',
      'order': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
    };
    return iconMap[type] || '//xian7.zos.ctyun.cn/pet/static/msg1.png';
  },

  // 点击消息项
  onMessageTap(e) {
    const { index } = e.currentTarget.dataset;
    const message = this.data.newsList[index];
    if (message) {
      // 标记为已读
      this.markAsRead(message.id, index);
      // 跳转到详情页
      wx.navigateTo({
        url: `/pages/message/messageDetail/index?id=${message.id}`
      });
    }
  },

  // 标记消息为已读
  async markAsRead(messageId, index) {
    try {
      await messageApi.markAsRead(messageId);
      // 更新本地数据
      const newsList = [...this.data.newsList];
      if (newsList[index]) {
        newsList[index].isRead = true;
        newsList[index].count = 0;
        this.setData({ newsList });
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  }
})
```

### 3.2 消息详情页面 (`pages/message/messageDetail/index.js`)

#### 主要功能
- 消息详情展示
- 自动标记已读
- 动态设置页面标题
- 解析和展示额外数据（订单信息等）

#### 核心代码结构
```javascript
Page({
  data: {
    messageId: '',
    messageDetail: null,
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ messageId: options.id });
      this.loadMessageDetail();
    }
  },

  // 加载消息详情
  async loadMessageDetail() {
    try {
      const res = await messageApi.getMessageDetail(this.data.messageId);
      if (res) {
        const messageDetail = {
          ...res,
          time: utils.formatNormalDate(res.createdAt),
          icon: this.getMessageIcon(res.type),
          parsedExtraData: this.parseExtraData(res.extraData)
        };
        this.setData({ messageDetail });

        // 动态设置页面标题
        const pageTitle = (res.title && res.title.trim()) ? res.title : '消息详情';
        wx.setNavigationBarTitle({
          title: pageTitle
        });

        // 如果消息未读，自动标记为已读
        if (!res.isRead) {
          this.markAsRead();
        }
      }
    } catch (error) {
      console.error('获取消息详情失败:', error);
      wx.showToast({
        title: '获取消息详情失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 解析额外数据
  parseExtraData(extraData) {
    if (!extraData) return null;

    try {
      let data = extraData;
      if (typeof extraData === 'string') {
        data = JSON.parse(extraData);
      }

      if (typeof data !== 'object' || data === null) {
        return null;
      }

      // 转换为显示格式
      const displayData = [];
      const fieldMap = {
        orderId: '订单ID',
        orderNo: '订单号',
        customerName: '客户姓名',
        serviceTime: '服务时间',
        serviceAddress: '服务地址',
        petInfo: '宠物信息',
        amount: '金额',
        remark: '备注'
      };

      for (const [key, value] of Object.entries(data)) {
        if (value !== null && value !== undefined && value !== '') {
          const label = fieldMap[key] || key;
          let displayValue = value;

          // 特殊处理时间格式
          if (key === 'serviceTime' && value) {
            displayValue = utils.formatNormalDate(value);
          }

          displayData.push({
            label,
            value: displayValue
          });
        }
      }

      return displayData.length > 0 ? displayData : null;
    } catch (error) {
      console.error('解析额外数据失败:', error);
      return null;
    }
  }
})
```

### 3.3 API接口层 (`api/modules/message.js`)

#### 主要功能
- 封装所有消息相关的API请求
- 统一错误处理
- 支持消息列表、详情、已读标记等操作

#### 核心代码结构
```javascript
import request, { analysisRes } from '../request';
import config from '../config';

const { message } = config.apiUrls;

export default {
  // 获取消息列表
  async getMessageList(userId, type = '') {
    const params = {};
    if (type) {
      params.type = type;
    }
    const res = await request.get(message.list.replace('{userId}', userId), params);
    const data = analysisRes(res);
    return data;
  },

  // 获取消息详情
  async getMessageDetail(messageId) {
    const res = await request.get(message.detail.replace('{messageId}', messageId), {});
    const data = analysisRes(res);
    return data;
  },

  // 标记消息为已读
  async markAsRead(messageId) {
    const res = await request.post(message.markAsRead.replace('{messageId}', messageId), {});
    const data = analysisRes(res);
    return data;
  },

  // 标记所有消息为已读
  async markAllAsRead(userId) {
    const res = await request.post(message.markAllAsRead.replace('{userId}', userId), {});
    const data = analysisRes(res);
    return data;
  },

  // 删除消息
  async deleteMessage(messageId) {
    const res = await request.delete(message.delete.replace('{messageId}', messageId));
    const data = analysisRes(res);
    return data;
  },

  // 获取未读消息数量
  async getUnreadCount(userId) {
    const res = await request.get(message.unreadCount.replace('{userId}', userId), {});
    const data = analysisRes(res);
    return data;
  },
};
```

## 4. 数据结构设计

### 4.1 消息数据结构
```javascript
{
  id: "消息ID",
  title: "消息标题",
  content: "消息内容",
  type: "消息类型(system/platform/order)",
  isRead: "是否已读(boolean)",
  count: "未读数量(number)",
  createdAt: "创建时间(ISO字符串)",
  extraData: "额外数据(JSON字符串)",
  // 前端处理后添加的字段
  icon: "消息图标URL",
  time: "格式化时间",
  parsedExtraData: "解析后的额外数据数组"
}
```

### 4.2 WebSocket通知结构
```javascript
{
  type: "通知类型(new_order/cancel_order/message)",
  message: "通知消息内容",
  data: {
    serviceType: {
      serviceType: "服务类型代码",
      serviceTypeName: "服务类型名称"
    },
    // 其他业务相关数据
  }
}
```

### 4.3 额外数据解析结构
```javascript
// extraData解析后的显示格式
[
  {
    label: "字段显示名称",
    value: "字段值"
  }
]
```

## 5. API接口设计

### 5.1 接口配置 (`api/config.js`)
```javascript
apiUrls: {
  message: {
    list: '/api/message/list/{userId}',           // GET - 获取消息列表
    detail: '/api/message/detail/{messageId}',    // GET - 获取消息详情
    markAsRead: '/api/message/mark-read/{messageId}',     // POST - 标记已读
    markAllAsRead: '/api/message/mark-all-read/{userId}', // POST - 全部标记已读
    delete: '/api/message/delete/{messageId}',    // DELETE - 删除消息
    unreadCount: '/api/message/unread-count/{userId}',    // GET - 未读数量
  }
}
```

### 5.2 接口说明

#### 获取消息列表
- **接口**: `GET /api/message/list/{userId}`
- **参数**:
  - `userId`: 用户ID（路径参数）
  - `type`: 消息类型（可选查询参数）
- **返回**: 消息列表数组

#### 获取消息详情
- **接口**: `GET /api/message/detail/{messageId}`
- **参数**: `messageId`: 消息ID（路径参数）
- **返回**: 消息详情对象

#### 标记消息已读
- **接口**: `POST /api/message/mark-read/{messageId}`
- **参数**: `messageId`: 消息ID（路径参数）
- **返回**: 操作结果

#### 获取未读数量
- **接口**: `GET /api/message/unread-count/{userId}`
- **参数**: `userId`: 用户ID（路径参数）
- **返回**: 未读消息数量

## 6. WebSocket实时通知

### 6.1 WebSocket连接管理 (`pages/index/index.js`)

#### 主要功能
- 建立和维护WebSocket连接
- 处理不同类型通知（订单、消息）
- 自动重连机制
- 职位权限过滤

#### 核心代码结构
```javascript
// 建立WebSocket连接
connectWebSocket: function () {
  const socketUrl = config.socketUrl;

  if (this.data.socketOpen) {
    console.log('Socket已连接，无需重复连接');
    return;
  }

  if (!this.socketListenersRegistered) {
    this.registerSocketListeners();
    this.socketListenersRegistered = true;
  }

  wx.connectSocket({
    url: socketUrl,
    success: res => {
      console.log('WebSocket连接成功');
      this.setData({ socketOpen: true });
      this.reconnectCount = 0;
    },
    fail: err => {
      console.error('WebSocket连接失败', err);
      this.setData({ socketOpen: false });
      this.reconnectWebSocket();
    },
  });
},

// 注册Socket监听器
registerSocketListeners: function() {
  // 监听WebSocket消息
  wx.onSocketMessage(res => {
    if (this.data.socketOpen) {
      this.handleNotification(res.data);
    }
  });

  // 监听WebSocket关闭
  wx.onSocketClose(res => {
    console.log('WebSocket连接关闭', res);
    this.setData({ socketOpen: false });
    if (getCurrentPages().length > 0) {
      const currentPage = getCurrentPages()[getCurrentPages().length - 1];
      if (currentPage.route === 'pages/index/index') {
        this.reconnectWebSocket();
      }
    }
  });

  // 监听WebSocket错误
  wx.onSocketError(res => {
    console.error('WebSocket连接错误', res);
    this.setData({ socketOpen: false });
    if (getCurrentPages().length > 0) {
      const currentPage = getCurrentPages()[getCurrentPages().length - 1];
      if (currentPage.route === 'pages/index/index') {
        this.reconnectWebSocket();
      }
    }
  });
},

// 处理接收到的通知
handleNotification: function (message) {
  try {
    const notification = JSON.parse(message);
    console.log('收到新通知', notification);
    switch (notification.type) {
      case 'new_order':
        this.handleOrderNotification(notification, 'new');
        break;
      case 'cancel_order':
        this.handleOrderNotification(notification, 'cancel');
        break;
      case 'message':
        wx.showToast({
          title: notification.message,
          icon: 'none',
          duration: 2000,
        });
        break;
    }
  } catch (error) {
    console.error('解析通知消息失败', error);
  }
},

// 重连WebSocket
reconnectWebSocket: function () {
  if (!this.reconnectCount) {
    this.reconnectCount = 0;
  }

  if (this.reconnectCount >= 5) {
    console.log('达到最大重连次数，停止重连');
    return;
  }

  this.reconnectCount++;
  const delay = Math.min(1000 * this.reconnectCount, 10000);

  console.log(`${delay}ms后进行第${this.reconnectCount}次重连`);

  clearTimeout(this.reconnectTimer);
  this.reconnectTimer = setTimeout(() => {
    if (getCurrentPages().length > 0) {
      const currentPage = getCurrentPages()[getCurrentPages().length - 1];
      if (currentPage.route === 'pages/index/index') {
        this.connectWebSocket();
      }
    }
  }, delay);
}
```

### 6.2 WebSocket配置
```javascript
// api/config.js
export default {
  baseUrl: 'http://127.0.0.1:3001',
  socketUrl: 'ws://127.0.0.1:3001',
  // 生产环境
  // socketUrl: "wss://manager.petsjoylife.com/ws/",
}
```

## 7. 样式设计

### 7.1 消息列表样式特点

#### 未读消息样式
```css
.message-unread {
  background-color: #f8f9ff;
  border-left: 4rpx solid #007aff;
}
```

#### 已读消息样式
```css
.message-read {
  opacity: 0.7;
}
```

#### 未读指示器
```css
.unread-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 12rpx;
  height: 12rpx;
  background-color: #ff3b30;
  border-radius: 50%;
}
```

### 7.2 消息详情样式特点

#### 未读消息头部
```css
.message-header.unread {
  background-color: #f8f9ff;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}
```

#### 已读状态标识
```css
.read-status {
  font-size: 22rpx;
  color: #28a745;
  background-color: #d4edda;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
}
```

#### 额外数据展示
```css
.message-extra {
  background-color: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}
```

## 8. 复用指南

### 8.1 必需文件清单

#### 核心页面文件
```
pages/message/
├── index.js                    # 消息列表页面逻辑
├── index.wxml                  # 消息列表页面模板
├── index.wxss                  # 消息列表页面样式
├── index.json                  # 消息列表页面配置
└── messageDetail/
    ├── index.js                # 消息详情页面逻辑
    ├── index.wxml              # 消息详情页面模板
    ├── index.wxss              # 消息详情页面样式
    └── index.json              # 消息详情页面配置
```

#### API和工具文件
```
api/
├── modules/
│   └── message.js              # 消息API接口封装
├── config.js                   # API配置文件
└── request.js                  # 请求封装

common/
└── Session.js                  # 用户会话管理

pages/utils/
└── util.js                     # 工具函数（时间格式化等）
```

#### 依赖组件
```
components/
├── diy-navbar/                 # 自定义导航栏
├── custom-tabbar/              # 自定义底部导航
└── empty/                      # 空状态组件
```

### 8.2 配置修改点

#### 1. API地址配置 (`api/config.js`)
```javascript
export default {
  // 修改为你的后端API地址
  baseUrl: 'https://your-api-domain.com',
  // 修改为你的WebSocket地址
  socketUrl: 'wss://your-websocket-domain.com',

  apiUrls: {
    message: {
      list: '/api/message/list/{userId}',
      detail: '/api/message/detail/{messageId}',
      markAsRead: '/api/message/mark-read/{messageId}',
      markAllAsRead: '/api/message/mark-all-read/{userId}',
      delete: '/api/message/delete/{messageId}',
      unreadCount: '/api/message/unread-count/{userId}',
    }
  }
}
```

#### 2. 消息图标资源路径 (`pages/message/index.js`)
```javascript
getMessageIcon(type) {
  const iconMap = {
    'system': 'https://your-cdn.com/icons/system.png',
    'platform': 'https://your-cdn.com/icons/platform.png',
    'order': 'https://your-cdn.com/icons/order.png',
  };
  return iconMap[type] || 'https://your-cdn.com/icons/default.png';
}
```

#### 3. 用户身份验证逻辑
根据你的项目调整用户登录验证和重定向逻辑：
```javascript
getUserInfo() {
  const userInfo = Session.getUser();
  if (!userInfo) {
    // 修改为你的登录页面路径
    return wx.redirectTo({
      url: "/pages/your-login-page/index",
    });
  }
  this.setData({ userInfo });
  this.loadMessageList();
}
```

#### 4. 页面路由配置 (`app.json`)
```json
{
  "pages": [
    "pages/message/index",
    "pages/message/messageDetail/index"
  ]
}
```

### 8.3 可选功能扩展

#### 1. 消息删除功能
```javascript
// 在消息列表页面添加删除功能
async deleteMessage(messageId, index) {
  try {
    await messageApi.deleteMessage(messageId);
    const newsList = [...this.data.newsList];
    newsList.splice(index, 1);
    this.setData({ newsList });
    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('删除消息失败:', error);
    wx.showToast({
      title: '删除失败',
      icon: 'none'
    });
  }
}
```

#### 2. 全部标记已读功能
```javascript
// 在消息列表页面添加全部已读功能
async markAllAsRead() {
  try {
    await messageApi.markAllAsRead(this.data.userInfo.id);
    const newsList = this.data.newsList.map(item => ({
      ...item,
      isRead: true,
      count: 0
    }));
    this.setData({ newsList });
    wx.showToast({
      title: '全部已读',
      icon: 'success'
    });
  } catch (error) {
    console.error('标记全部已读失败:', error);
  }
}
```

#### 3. 消息分页加载
```javascript
// 在消息列表页面添加分页功能
data: {
  page: 1,
  pageSize: 20,
  hasMore: true
},

async loadMessageList(isRefresh = false) {
  if (this.data.loading) return;

  if (isRefresh) {
    this.setData({
      page: 1,
      hasMore: true,
      newsList: []
    });
  }

  if (!this.data.hasMore) return;

  this.setData({ loading: true });
  try {
    const res = await messageApi.getMessageList(
      this.data.userInfo.id,
      '',
      this.data.page,
      this.data.pageSize
    );

    const { list, total } = res || {};
    if (list && list.length >= 0) {
      const formattedList = list.map(item => ({
        ...item,
        time: utils.formatDate(new Date(item.createdAt)),
        icon: this.getMessageIcon(item.type)
      }));

      const newsList = isRefresh ? formattedList : [...this.data.newsList, ...formattedList];
      const hasMore = newsList.length < total;

      this.setData({
        newsList,
        hasMore,
        page: this.data.page + 1
      });
    }
  } catch (error) {
    console.error('获取消息列表失败:', error);
  } finally {
    this.setData({ loading: false });
  }
}
```

### 8.4 注意事项

1. **权限验证**: 确保后端API有适当的用户权限验证
2. **错误处理**: 根据项目需求调整错误提示方式
3. **样式适配**: 根据项目UI规范调整样式
4. **WebSocket**: 确保WebSocket服务端支持相应的消息格式
5. **性能优化**: 大量消息时考虑虚拟列表或分页加载
6. **离线处理**: 考虑网络异常时的本地缓存策略

## 9. 技术要点

### 9.1 关键技术特性

1. **状态管理**: 使用微信小程序原生data进行状态管理，简单高效
2. **网络请求**: 统一的request封装，支持错误处理和响应拦截
3. **实时通知**: WebSocket + 自动重连机制，确保消息实时性
4. **用户体验**: 下拉刷新、加载状态、空状态处理，提升用户体验
5. **数据格式化**: 时间格式化、额外数据解析，数据展示友好
6. **权限控制**: 基于用户职位的通知过滤，避免无关通知干扰

### 9.2 性能优化建议

1. **列表优化**: 大量消息时使用虚拟滚动或分页加载
2. **图片优化**: 消息图标使用CDN，支持懒加载
3. **缓存策略**: 合理使用本地存储缓存消息数据
4. **网络优化**: 避免重复请求，合理设置请求超时时间

### 9.3 安全考虑

1. **数据验证**: 前端接收数据时进行格式验证
2. **XSS防护**: 消息内容显示时注意防止XSS攻击
3. **权限控制**: 确保用户只能访问自己的消息
4. **敏感信息**: 避免在前端存储敏感的用户信息

### 9.4 扩展性设计

1. **模块化**: 各功能模块独立，便于维护和扩展
2. **配置化**: 关键配置项可配置，适应不同环境
3. **组件化**: UI组件可复用，减少重复代码
4. **插件化**: 支持功能插件扩展，如推送、统计等

---

## 总结

本消息系统设计完整，功能齐全，具有良好的可维护性和扩展性。通过模块化的设计和详细的文档说明，可以快速复用到其他微信小程序项目中。主要优势包括：

- **完整的功能覆盖**: 从消息列表到详情，从实时通知到状态管理
- **良好的用户体验**: 响应式设计，加载状态，错误处理
- **高度可配置**: 关键配置项可根据项目需求调整
- **易于扩展**: 模块化设计，支持功能扩展和定制

在实际使用时，只需要根据项目的具体需求调整API接口、样式和业务逻辑即可快速集成。
