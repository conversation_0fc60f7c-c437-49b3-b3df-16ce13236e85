# 系统颜色规范

## 主色调

### 蓝色系（主要操作）
- **主蓝色**: `#007aff` - 主要按钮、链接、强调色
- **深蓝色**: `#0056cc` - 按钮按下状态、深色变体
- **浅蓝色**: `#f0f8ff` - 背景色、淡化效果

### 绿色系（成功/确认）
- **主绿色**: `#34c759` - 成功状态、确认按钮
- **深绿色**: `#30d158` - 绿色按钮的渐变终点
- **浅绿色**: `rgba(52, 199, 89, 0.1)` - 绿色背景

### 红色系（警告/删除）
- **主红色**: `#ff3b30` - 警告、删除按钮
- **深红色**: `#d70015` - 红色按钮的渐变终点
- **浅红色**: `rgba(255, 59, 48, 0.3)` - 红色阴影

### 橙色系（次要操作）
- **主橙色**: `#ff9500` - 次要操作、标签
- **深橙色**: `#ff6b00` - 橙色的深色变体
- **浅橙色**: `#fff5e6` - 橙色背景

## 中性色

### 文字颜色
- **主文字**: `#333` - 标题、重要文字
- **次文字**: `#666` - 描述文字、标签
- **辅助文字**: `#999` - 提示文字、占位符

### 背景颜色
- **页面背景**: `#f5f5f5` - 主要页面背景
- **卡片背景**: `#fff` - 卡片、表单背景
- **分割背景**: `#f8f9fa` - 分割区域背景

### 边框颜色
- **主边框**: `#e0e0e0` - 输入框、卡片边框
- **浅边框**: `#f0f0f0` - 分割线、淡边框

## 渐变色方案

### 主要渐变
```css
/* 蓝色渐变 - 主要操作 */
background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);

/* 绿色渐变 - 成功/开始 */
background: linear-gradient(135deg, #34c759 0%, #30d158 100%);

/* 红色渐变 - 警告/结束 */
background: linear-gradient(135deg, #ff3b30 0%, #d70015 100%);

/* 橙色渐变 - 次要操作 */
background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
```

### 背景渐变
```css
/* 蓝色背景渐变 */
background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);

/* 橙色背景渐变 */
background: linear-gradient(135deg, #fff5e6 0%, #ffebcc 100%);
```

## 阴影效果

### 按钮阴影
```css
/* 蓝色按钮阴影 */
box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);

/* 绿色按钮阴影 */
box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.3);

/* 红色按钮阴影 */
box-shadow: 0 4rpx 16rpx rgba(255, 59, 48, 0.3);

/* 卡片阴影 */
box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
```

## 应用场景

### 按钮颜色
- **主要操作**: 蓝色渐变 (`#007aff` → `#0056cc`)
- **确认/开始**: 绿色渐变 (`#34c759` → `#30d158`)
- **警告/删除**: 红色渐变 (`#ff3b30` → `#d70015`)
- **次要操作**: 橙色渐变 (`#ff9500` → `#ff6b00`)

### 状态标签
- **进行中**: 蓝色 (`#007aff`)
- **已完成**: 绿色 (`#34c759`)
- **警告**: 红色 (`#ff3b30`)
- **待处理**: 橙色 (`#ff9500`)

### 页面元素
- **导航栏**: 白色背景 (`#fff`)，黑色文字
- **卡片**: 白色背景，淡灰色边框
- **输入框**: 白色背景，聚焦时蓝色边框

## 已统一的页面

### 服务时长统计页面
- ✅ 页面头部：蓝色渐变
- ✅ 计数徽章：红色/绿色渐变
- ✅ 服务类型标签：蓝色/橙色渐变
- ✅ 时长显示：蓝色主题
- ✅ 按钮：绿色开始、红色结束、蓝色其他

### 车辆管理页面
- ✅ 按钮：蓝色主题
- ✅ 状态显示：系统标准色

## 使用原则

1. **一致性**: 相同功能使用相同颜色
2. **层次性**: 重要程度通过颜色深浅体现
3. **可访问性**: 确保足够的对比度
4. **品牌性**: 以蓝色为主色调，体现专业性

## 注意事项

1. 避免使用过于鲜艳的颜色
2. 保持颜色的语义化（红色=警告，绿色=成功）
3. 新增页面应遵循此颜色规范
4. 定期检查颜色使用的一致性
