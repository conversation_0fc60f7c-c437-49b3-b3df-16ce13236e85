<!-- 待确认的追加服务 -->
<view class="additional-services" wx:if="{{pendingAdditionalServices.length > 0}}">
  <view class="service-item" wx:for="{{pendingAdditionalServices}}" wx:key="id">
    <view class="service-info">
      <view class="service-header">
        <text class="service-name">{{item.details[0].serviceName || item.serviceName}}</text>
        <text class="service-status pending">待确认</text>
      </view>

      <!-- 客户信息 -->
      <view class="service-customer-info">
        <text class="customer-label">客户：</text>
        <text class="customer-name">{{item.customerName || item.customer.nickname || '未知客户'}}</text>
        <text class="customer-phone" wx:if="{{item.customerPhone || item.customer.phone}}">({{item.customerPhone || item.customer.phone}})</text>
      </view>

      <!-- 订单信息 -->
      <view class="service-details">
        <view class="detail-row">
          <text class="detail-label">订单号：</text>
          <text class="detail-value">{{item.orderSn || item.sn}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">数量：</text>
          <text class="detail-value">{{item.details[0].quantity || 1}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.createdAt}}">
          <text class="detail-label">申请时间：</text>
          <text class="detail-value detail-time">{{item.createdAt}}</text>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="service-price-info">
        <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > 0}}">
          <text class="price-label">原价：</text>
          <text class="original-price">¥{{item.originalPrice}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">实付：</text>
          <text class="current-price">¥{{item.totalFee || item.details[0].servicePrice}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="service-actions">
      <view class="service-action-btn confirm-btn" bindtap="onConfirmService" data-service="{{item}}">
        确认
      </view>
      <view class="service-action-btn reject-btn" bindtap="onRejectService" data-service="{{item}}">
        拒绝
      </view>
    </view>
  </view>
</view>
