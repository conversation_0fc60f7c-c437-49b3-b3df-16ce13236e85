// pages/mine/index.js
import siteinfo from '../../siteinfo.js';
import userApi from '../../api/modules/user.js';
import Session from '../../common/Session.js';
import LocationManager from '../../utils/LocationManager.js';
import positionUtils from '../../utils/positionUtils.js';

Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		siteinfo,
		userInfo: {},
		userStats: {
			averageRating: 5,
			totalReviews: 0,
			stars: ['filled', 'filled', 'filled', 'filled', 'filled']
		},
		showModal: false,
		modalTitle: '敬请期待',
		modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
		locationServiceStatus: false, // 位置服务状态
	},

	onLoad() {
		this.initUserInfo();
	},

	onShow() {
		// 每次显示页面时都更新用户信息
		this.updateUserInfo();
		// 更新位置服务状态
		this.updateLocationServiceStatus();
	},

	// 初始化用户信息
	initUserInfo() {
		const userInfo = Session.getUser();
		console.log('当前用户信息:', userInfo);
		if (!userInfo || !userInfo.id) {
			return wx.redirectTo({
				url: '/pages/login/index',
			});
		}
		this.setData({ userInfo });
		this.updateUserInfo();
	},

	// 更新用户信息和统计数据
	async updateUserInfo() {
		const userInfo = Session.getUser();
		if (!userInfo || !userInfo.id) {
			return;
		}

		try {
			// 获取员工详细信息（包括评价数据和职位信息）
			const employeeInfo = await userApi.getEmployeeInfo(userInfo.id);

			if (employeeInfo) {
				// 获取职位名称
				let positionName = '';
				if (employeeInfo.position) {
					positionName = await positionUtils.getPositionName(employeeInfo.position);
				}

				// 更新用户信息
				const newUserInfo = {
					...userInfo,
					...employeeInfo,
					positionName // 添加职位名称
				};
				Session.setUser(newUserInfo);
				this.setData({ userInfo: newUserInfo });

				// 从员工信息中提取评价统计数据
				const rating = employeeInfo.rating || 0;
				const userStats = {
					averageRating: rating,
					totalReviews: employeeInfo.totalReviews || 0, // 这个暂时没有
					stars: this.generateStars(rating)
				};
				this.setData({ userStats });

				console.log('用户信息更新成功:', employeeInfo);
			}
		} catch (error) {
			console.error('更新用户信息失败:', error);
			// 静默失败，不影响用户体验
		}
	},

	/**
	 * 生成星级数组
	 * @param {number} rating 评分 (0-5)
	 * @returns {Array} 星级数组，包含5个元素，每个元素表示星星的状态
	 */
	generateStars(rating) {
		const stars = [];
		for (let i = 1; i <= 5; i++) {
			if (i <= Math.floor(rating)) {
				stars.push('filled'); // 实心星
			} else if (i === Math.ceil(rating) && rating % 1 >= 0.5) {
				stars.push('half'); // 半星
			} else {
				stars.push('empty'); // 空心星
			}
		}
		return stars;
	},

	redirect(evt) {
		let { type } = evt.currentTarget.dataset;
		let url;
		switch (type) {
			case 'user':
				url = '/pages/mine/userAgreement/index';
				break;
			case 'previate':
				url = '/pages/mine/privacyAgreement/index';
				break;
			case 'login':
				url = '/pages/login/index';
				break;
			case 'complaint':
				url = '/pages/complaint/index';
				break;
			case 'promotion':
				url = '/pages/mine/promotion/index';
				break;
			case 'checkin':
				url = '/pages/mine/checkin/index';
				break;
			case 'serviceDuration':
				url = '/pages/serviceDuration/index';
				break;
			case 'vehicle':
				url = '/pages/mine/vehicle/index';
				break;

			default:
				break;
		}
		if (url) {
			wx.navigateTo({
				url,
			});
		} else {
			// 显示自定义模态框
			this.setData({
				showModal: true,
			});
		}
	},

	uploadAvatar() {
		this.uploadImage(
			this,
			'userInfo.avatar', // 想把url存入哪个字段
			[],
			`avatar/test.png`, // 上传的key,登录avatar/00001.png
			1
		);
	},

	// 模态框确认事件
	onModalConfirm() {
		this.setData({
			showModal: false,
		});
	},

	// 更新位置服务状态
	updateLocationServiceStatus() {
		const isRunning = LocationManager.isLocationServiceRunning();
		this.setData({
			locationServiceStatus: isRunning,
		});
	},

	// 切换位置服务状态（调试用）
	toggleLocationService() {
		const userInfo = Session.getUser();
		if (!userInfo || !userInfo.id) {
			console.log('位置服务: 用户未登录');
			return;
		}

		if (LocationManager.isLocationServiceRunning()) {
			LocationManager.stop();
			console.log('位置服务: 手动停止');
		} else {
			LocationManager.start();
			console.log('位置服务: 手动启动');
		}

		// 更新状态显示
		setTimeout(() => {
			this.updateLocationServiceStatus();
		}, 500);
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},
});
