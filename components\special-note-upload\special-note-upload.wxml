<view wx:if="{{show}}" class="modal-mask" bindtap="onCancel">
  <view class="modal-container" catchtap="preventClose">
    <view class="modal-content {{readonly ? 'readonly' : ''}} {{uploading ? 'uploading' : ''}}" catchtap="preventClose">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">特殊情况说明</text>
        <view class="close-btn" bindtap="onCancel">×</view>
      </view>

      <!-- 弹窗主体 -->
      <view class="modal-body">
        <!-- 文字输入区域 -->
        <view class="content-section">
          <view class="section-title">情况描述</view>
          <textarea
            class="content-textarea"
            placeholder="{{readonly ? '暂无特殊情况说明' : '请描述遇到的特殊情况，如宠物情绪、环境问题等...'}}"
            value="{{content}}"
            bindinput="onContentInput"
            maxlength="{{maxContentLength}}"
            disabled="{{readonly}}"
          ></textarea>
          <view class="char-count" wx:if="{{!readonly}}">{{content.length}}/{{maxContentLength}}</view>
        </view>

        <!-- 图片上传区域 -->
        <view class="photo-section">
          <view class="section-title">相关图片</view>
          <view class="photo-container">
            <!-- 已上传的图片 -->
            <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
              <image
                src="{{item}}"
                class="photo-preview"
                mode="aspectFill"
                bindtap="previewImage"
                data-url="{{item}}"
              ></image>
              <view wx:if="{{!readonly}}" class="photo-delete" catchtap="deletePhoto" data-index="{{index}}">
                ×
              </view>
            </view>
            
            <!-- 上传按钮 -->
            <view 
              wx:if="{{!readonly && photoList.length < maxCount && !uploading}}" 
              class="photo-upload" 
              bindtap="chooseImage"
            >
              <image class="upload-image" src="//xian7.zos.ctyun.cn/pet/static/upload-image.png" mode="aspectFit"></image>
            </view>
          </view>

          <!-- 空状态提示 -->
          <view wx:if="{{photoList.length === 0 && readonly}}" class="empty-state">
            <image class='empty-icon' src="//xian7.zos.ctyun.cn/pet/static/zwtp.png" mode="aspectFill"></image>
            <text>暂无相关图片</text>
          </view>
        </view>
      </view>

      <!-- 弹窗底部 -->
      <view class="modal-footer">
        <button class="footer-btn cancel-btn" bindtap="onCancel">{{readonly ? '关闭' : '取消'}}</button>
        
        <button wx:if="{{!readonly}}" class="footer-btn confirm-btn" bindtap="onConfirm">确认保存</button>
        
        <button wx:if="{{!readonly && noteData}}" class="footer-btn delete-btn" bindtap="onDelete">删除说明</button>
      </view>
    </view>
  </view>
</view>
