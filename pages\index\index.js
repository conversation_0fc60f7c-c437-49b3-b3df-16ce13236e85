import dictionaryApi from '../../api/modules/dictionary';
import orderApi from '../../api/modules/order';
import checkinApi from '../../api/modules/employee-checkin';
import { formatDate, formatNormalDate } from '../utils/util';
import config from '../../api/config';
import Session from '../../common/Session';
import positionUtils from '../../utils/positionUtils';
import AddressUtils from '../../utils/AddressUtils';

Page({
  data: {
    userInfo: null,
    // 订单类型标签
    orderTabs: [],
    currentTab: '', // 当前选中的标签
    orderList: [], // 订单列表
    page: 1, // 当前页码
    pageSize: 50, // 每页数量
    // 接单确认弹窗
    showConfirmModal: false,
    confirmModalTitle: '接单确认',
    confirmModalContent: '确定要接此订单吗？',
    confirmModalBtnText: '确认接单',
    pendingOrderId: '', // 待接单的订单ID
    // 强制打卡相关
    showForceCheckinModal: false,
    lastCheckinDays: 0,
    isCheckingCheckinStatus: false,
  },

  onLoad() {
    const _this = this;
    const userInfo = Session.getUser();
    this.setData({ userInfo });

    if (!userInfo) {
      return wx.redirectTo({
        url: '/pages/login/index',
      });
    } else {
      // 检查打卡状态
      this.checkCheckinStatus().then(() => {
        // 加载字典数据
        dictionaryApi.list('服务类型').then(res => {
          this.setData({
            orderTabs: (res || []).map(item => {
              return {
                name: item.name + '单',
                status: item.code,
              };
            }),
            currentTab: (res || [])?.[0]?.code,
          });
          // 加载初始订单数据
          _this.loadOrders();
        });
      });
    }

    // 建立socket连接
    this.connectWebSocket();
  },

  // 检查打卡状态
  async checkCheckinStatus() {
    if (this.data.isCheckingCheckinStatus) {
      return;
    }

    // 检查用户登录状态
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      console.log('用户未登录，跳过打卡状态检查');
      return;
    }

    this.setData({ isCheckingCheckinStatus: true });

    try {
      const needForceCheckin = await checkinApi.checkNeedForceCheckin(userInfo.id);

      if (needForceCheckin) {
        // 获取完整的打卡信息来计算天数
        const checkinInfo = await checkinApi.getLastCheckinInfo(userInfo.id);
        let lastCheckinDays = 0;

        if (checkinInfo && checkinInfo.lastCheckInTime) {
          const lastCheckin = new Date(checkinInfo.lastCheckInTime);
          const now = new Date();
          const diffTime = now.getTime() - lastCheckin.getTime();
          lastCheckinDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          console.log(`显示强制打卡弹窗，距离上次打卡 ${lastCheckinDays} 天`);
        } else {
          console.log('显示强制打卡弹窗，员工从未打卡');
        }

        this.setData({
          showForceCheckinModal: true,
          lastCheckinDays: lastCheckinDays
        });
      } else {
        // 不需要强制打卡，确保弹窗关闭
        console.log('不需要强制打卡，关闭弹窗');
        this.setData({
          showForceCheckinModal: false,
          lastCheckinDays: 0
        });
      }
    } catch (error) {
      console.error('检查打卡状态失败:', error);
      // 出错时不影响正常使用，静默处理
    } finally {
      this.setData({ isCheckingCheckinStatus: false });
    }
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
    });
    this.loadOrders();
  },

  // 加载订单数据
  async loadOrders() {
    wx.showLoading({
      title: '加载中',
    });

    const { id } = this.data.userInfo;
    const res = await orderApi.list(id, this.data.currentTab);
    let newData = res?.list.map(item => {
      return {
        ...item,
        serviceName: item.orderDetails.map(item => item.service.serviceName),
        serviceLogo: item.orderDetails.map(item => item.service.logo)[0],
        orderTime: formatDate(item.orderTime),
        expectTime: formatNormalDate(item.serviceTime), // 期待服务时间
        originalPrice: item.originalPrice, // 原价
        totalFee: item.totalFee, // 实付金额
      };
    }) || [];

    // 根据员工职位过滤可接的订单（前端辅助过滤，提升用户体验）
    if (this.data.userInfo && this.data.userInfo.position) {
      const allowedServiceTypes = positionUtils.getAllowedServiceTypes(this.data.userInfo.position);

      if (allowedServiceTypes.length > 0) {
        newData = newData.filter(order => {
          if (!order.orderDetails || order.orderDetails.length === 0) return true;
          const serviceType = order.orderDetails[0].service?.serviceType;
          return !serviceType || allowedServiceTypes.includes(serviceType);
        });
      }
    }

    this.setData({
      orderList: newData,
    });

    wx.hideLoading();
  },

  // 根据状态过滤订单
  filterOrdersByType(orders) {
    return orders.filter(order => order.type === this.data.currentTab);
  },

  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },

  // 加载更多订单
  loadMoreOrders() {
    this.setData({
      page: this.data.page + 1,
    });
    this.loadOrders();
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.orderId;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation && e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index`,
    });
  },

  // 打开导航
  openNavigation(e) {
    e.stopPropagation && e.stopPropagation(); // 阻止事件冒泡，避免触发订单详情页面跳转
    const { address, remark, latitude, longitude } = e.currentTarget.dataset;

    // 使用公共地址工具打开导航
    AddressUtils.openNavigation({
      address,
      remark,
      latitude,
      longitude
    });
  },

  // 删除订单
  deleteOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '删除订单',
      content: '确定要删除此订单吗？',
      success: res => {
        if (res.confirm) {
          // 实际项目中调用删除订单API
          const orderList = this.data.orderList.filter(order => order.orderId !== orderId);
          this.setData({
            orderList,
          });
          wx.showToast({
            title: '删除成功',
            icon: 'success',
          });
        }
      },
    });
  },

  // 催接单
  confirmReceipt(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);},

  // 显示接单确认弹窗
  async reschedule(e) {
    const orderId = e.currentTarget.dataset.orderId;
    const userInfo = this.data.userInfo;

    // 查找对应的订单信息
    const orderInfo = this.data.orderList.find(order => order.id === orderId);

    // 检查职位限制（前端预检查，提升用户体验）
    if (userInfo && userInfo.position && orderInfo && orderInfo.orderDetails && orderInfo.orderDetails.length > 0) {
      const serviceType = orderInfo.orderDetails[0].service?.serviceType;

      if (serviceType && !positionUtils.canAcceptOrder(userInfo.position, serviceType)) {
        // 显示职位限制提示
        const message = await positionUtils.getPositionLimitMessage(userInfo.position, serviceType);
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 3000,
        });
        return;
      }
    }

    this.setData({
      showConfirmModal: true,
      pendingOrderId: orderId,
    });
  },

  // 确认接单
  async onConfirmAccept() {
    // 隐藏确认弹窗
    this.setData({
      showConfirmModal: false,
    });

    wx.showLoading({
      title: '接单中',
    });

    const orderId = this.data.pendingOrderId;
    const userId = this.data.userInfo.id;

    try {
      const res = await orderApi.accept(orderId, userId);
      if (res) {
        wx.showToast({
          title: '接单成功',
          icon: 'success',
          // duration: 1000, // 显示1秒
          mask: true, // 防止用户点击
        });

        // 0.5秒后跳转到我的订单页面（给toast足够的显示时间）
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/orders/index',
          });
        }, 500);
      } else {
        // 接单失败，可能是职位限制或其他原因
        // 错误信息已在 analysisRes 中处理，这里不需要额外提示
        console.log('接单失败，可能是职位限制');
      }
    } catch (error) {
      console.error('接单异常:', error);
      // 网络错误或其他异常
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 刷新订单列表，移除已被其他员工接单或不符合职位要求的订单
      this.loadOrders();
    }
  },

  // 取消接单
  onCancelAccept() {
    this.setData({
      showConfirmModal: false,
      pendingOrderId: '',
    });
  },

  // ----------------------------socket管理-----------------------
  // 建立WebSocket连接
  connectWebSocket: function () {
    // 替换为实际的后端WebSocket地址
    const socketUrl = config.socketUrl;

    // 检查是否已有连接
    if (this.data.socketOpen) {
      console.log('Socket已连接，无需重复连接');
      return;
    }

    console.log('开始建立Socket连接:', socketUrl);

    // 只在第一次连接时注册监听器
    if (!this.socketListenersRegistered) {
      this.registerSocketListeners();
      this.socketListenersRegistered = true;
    }

    // 打开WebSocket连接
    wx.connectSocket({
      url: socketUrl,
      success: res => {
        console.log('WebSocket连接成功');
        this.setData({
          socketOpen: true,
        });
        // 重置重连计数
        this.reconnectCount = 0;
      },
      fail: err => {
        console.error('WebSocket连接失败', err);
        this.setData({
          socketOpen: false,
        });
        // 重连逻辑
        this.reconnectWebSocket();
      },
    });
  },

  // 注册Socket监听器（只注册一次）
  registerSocketListeners: function() {
    console.log('注册Socket监听器');

    // 监听WebSocket消息
    wx.onSocketMessage(res => {
      if (this.data.socketOpen) {
        this.handleNotification(res.data);
      }
    });

    // 监听WebSocket关闭
    wx.onSocketClose(res => {
      console.log('WebSocket连接关闭', res);
      this.setData({
        socketOpen: false,
      });
      // 只有在首页时才自动重连
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.reconnectWebSocket();
        }
      }
    });

    // 监听WebSocket错误
    wx.onSocketError(res => {
      console.error('WebSocket连接错误', res);
      this.setData({
        socketOpen: false,
      });
      // 只有在首页时才重连
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.reconnectWebSocket();
        }
      }
    });
  },

  // 处理接收到的通知
  handleNotification: function (message) {
    try {
      const notification = JSON.parse(message);
      console.log('收到新通知', notification);
      switch (notification.type) {
        case 'new_order':
          // 检查职位限制，只处理在自己接单范围内的订单
          this.handleOrderNotification(notification, 'new');
          break;
        case 'cancel_order':
          // 检查职位限制，只处理在自己接单范围内的订单
          this.handleOrderNotification(notification, 'cancel');
          break;
        case 'message':
          // 更新消息列表
          wx.showToast({
            title: notification.message,
            icon: 'none',
            duration: 2000,
          });
          break;
      }
    } catch (error) {
      console.error('解析通知消息失败', error);
    }
  },

  // 处理订单相关通知（新增/取消）
  async handleOrderNotification(notification, type) {
    const userInfo = this.data.userInfo;

    // 如果没有用户信息或职位信息，直接处理（兼容处理）
    if (!userInfo || !userInfo.position) {
      this.processOrderNotification(type);
      return;
    }

    // 检查通知中是否包含订单服务类型信息
    // 根据后台数据结构：notification.data.serviceType.serviceType
    if (notification.data && notification.data.serviceType && notification.data.serviceType.serviceType) {
      const serviceType = notification.data.serviceType.serviceType;

      // 检查是否在职位允许范围内
      const allowedServiceTypes = positionUtils.getAllowedServiceTypes(userInfo.position);

      if (allowedServiceTypes.length > 0 && !allowedServiceTypes.includes(serviceType)) {
        // 不在接单范围内，不处理此通知
        const serviceTypeName = notification.data.serviceType.serviceTypeName || serviceType;
        console.log(`职位限制：${userInfo.position} 不处理 ${serviceTypeName}(${serviceType}) 类型的订单通知`);
        return;
      }
    }

    // 在接单范围内，正常处理通知
    this.processOrderNotification(type);
  },

  // 执行订单通知处理
  processOrderNotification(type) {
    // 播放通知音效（可选）
    this.playNotificationSound(type);
    // 更新订单列表
    this.loadOrders();
  },

  // 播放通知音效
  playNotificationSound: function (type) {
    // 使用小程序的音频API播放通知音效
    const audioContext = wx.createInnerAudioContext();
    if (type === 'new') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message.mp3';
    }
    if (type === 'cancel') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message1.mp3';
    }
    audioContext.autoplay = true;
    audioContext.play();
  },

  // 重连WebSocket
  reconnectWebSocket: function () {
    // 初始化重连计数
    if (!this.reconnectCount) {
      this.reconnectCount = 0;
    }

    // 限制重连次数
    if (this.reconnectCount >= 5) {
      console.log('达到最大重连次数，停止重连');
      return;
    }

    this.reconnectCount++;
    const delay = Math.min(1000 * this.reconnectCount, 10000); // 递增延迟，最大10秒

    console.log(`${delay}ms后进行第${this.reconnectCount}次重连`);

    // 延迟重连，避免频繁连接
    clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      // 再次检查是否在首页
      if (getCurrentPages().length > 0) {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
        if (currentPage.route === 'pages/index/index') {
          this.connectWebSocket();
        }
      }
    }, delay);
  },



  // 页面隐藏时保持连接（不断开）
  onHide: function () {
    console.log('首页隐藏，保持Socket连接');
    // 清除重连定时器，避免在后台重连
    clearTimeout(this.reconnectTimer);
  },

  // 页面卸载时关闭连接
  onUnload: function () {
    console.log('首页卸载，关闭Socket连接');
    clearTimeout(this.reconnectTimer);
    if (this.data.socketOpen) {
      wx.closeSocket();
      this.setData({
        socketOpen: false,
      });
    }
  },

  // 处理强制打卡弹窗的前往打卡事件
  onForceCheckinGoto() {
    wx.navigateTo({
      url: '/pages/mine/checkin/index?force=true'
    });
  },

  // 手动关闭强制打卡弹窗（调试用）
  closeForceCheckinModal() {
    this.setData({
      showForceCheckinModal: false,
      lastCheckinDays: 0
    });
  },

  // 页面显示时重新检查打卡状态
  onShow: function () {
    // 如果socket断开了，重新连接
    if (!this.data.socketOpen) {
      console.log('页面显示时检测到Socket断开，重新连接');
      this.reconnectCount = 0; // 重置重连计数
      this.connectWebSocket();
    }

    // 检查用户登录状态
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      console.log('用户未登录，跳过打卡状态检查');
      return;
    }

    // 更新用户信息
    this.setData({ userInfo });

    // 检查是否刚完成强制打卡
    const forceCheckinCompleted = wx.getStorageSync('forceCheckinCompleted');
    if (forceCheckinCompleted) {
      console.log('检测到强制打卡已完成，关闭弹窗');
      // 清除标记
      wx.removeStorageSync('forceCheckinCompleted');
      // 关闭强制打卡弹窗
      this.setData({
        showForceCheckinModal: false,
        lastCheckinDays: 0
      });
      return;
    }

    // 如果当前显示强制打卡弹窗，延迟重新检查打卡状态
    if (this.data.showForceCheckinModal) {
      console.log('检测到强制打卡弹窗显示中，延迟重新检查打卡状态');
      setTimeout(() => {
        this.checkCheckinStatus();
      }, 1000); // 延迟1秒重新检查
    } else {
      // 正常检查打卡状态
      this.checkCheckinStatus();
    }
  },
});
