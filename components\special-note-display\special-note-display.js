Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    specialNoteData: {
      type: Object,
      value: null
    }
  },

  data: {},

  methods: {
    // 显示特殊情况说明弹窗
    onShowSpecialNote() {
      this.triggerEvent('showSpecialNote');
    },

    // 预览特殊情况说明图片
    onPreviewPhoto(e) {
      const url = e.currentTarget.dataset.url;
      const urls = e.currentTarget.dataset.urls;
      
      wx.previewImage({
        current: url,
        urls: urls
      });
    }
  }
});
