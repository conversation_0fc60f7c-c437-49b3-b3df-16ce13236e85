Component({
  properties: {
    // 服务照片数据
    servicePhotos: {
      type: Object,
      value: null
    },
    // 是否有服务照片
    hasServicePhotos: {
      type: Boolean,
      value: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    }
  },

  data: {},

  methods: {
    // 预览服务前照片
    previewBeforePhoto(e) {
      const { current } = e.currentTarget.dataset;
      const { servicePhotos } = this.data;
      if (servicePhotos && servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) {
        wx.previewImage({
          current: current,
          urls: servicePhotos.beforePhotos,
        });
      }
    },

    // 预览服务后照片
    previewAfterPhoto(e) {
      const { current } = e.currentTarget.dataset;
      const { servicePhotos } = this.data;
      if (servicePhotos && servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0) {
        wx.previewImage({
          current: current,
          urls: servicePhotos.afterPhotos,
        });
      }
    }
  }
});
