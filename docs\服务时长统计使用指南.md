# 服务时长统计功能使用指南

## 📱 快速上手指南

### 🎯 功能入口
1. **订单详情页面** → 服务时长统计模块
2. **我的页面** → 服务时长菜单

---

## 🚀 完整操作流程

### 第一步：接单并开始服务

#### 1.1 在订单列表页面
```
订单列表 → 选择"待服务"订单 → 点击"开始服务"
```

#### 1.2 系统自动操作
- ✅ 订单状态变更为"服务中"
- ✅ 主服务自动开始计时
- ✅ 可选择上传服务前照片

### 第二步：进入服务时长统计

#### 2.1 进入订单详情
```
订单列表 → 点击"服务中"订单 → 进入订单详情页面
```

#### 2.2 找到服务时长模块
```
订单详情页面 → 向下滚动 → 找到"服务时长统计"卡片
```

### 第三步：管理主服务时长

#### 3.1 查看主服务状态
```
┌─────────────────────────────────────┐
│ 🕐 服务时长统计                      │
├─────────────────────────────────────┤
│ 主服务                               │
│ ┌─────────────────────────────────┐ │
│ │ 小体猫洗护        [进行中] [完成] │ │
│ │ 开始时间：2025-07-01 11:27:18   │ │
│ │ 已用时：15分钟                  │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 3.2 完成主服务
1. 点击主服务右侧的 **[完成]** 按钮
2. 系统弹出确认对话框：
   ```
   确认完成服务
   确定要完成"小体猫洗护"服务吗？
   [取消] [确定]
   ```
3. 点击 **[确定]** 完成服务
4. 状态变更为"已完成"，显示总用时

### 第四步：管理增项服务（如有）

#### 4.1 查看增项服务列表
```
┌─────────────────────────────────────┐
│ 增项服务                             │
│ ┌─────────────────────────────────┐ │
│ │ 美容护理          [未开始] [开始] │ │
│ │ 状态：已确认                    │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 4.2 开始增项服务
1. 点击增项服务右侧的 **[开始]** 按钮
2. 系统弹出确认对话框：
   ```
   开始服务计时
   确定要开始"美容护理"服务吗？
   [取消] [确定]
   ```
3. 点击 **[确定]** 开始计时
4. 按钮变为 **[完成]**，开始显示实时用时

#### 4.3 完成增项服务
1. 服务执行完毕后，点击 **[完成]** 按钮
2. 确认完成操作
3. 状态变更为"已完成"

### 第五步：完成整体服务

#### 5.1 自动检测提醒
当所有服务都完成后，系统会自动弹出提醒：
```
┌─────────────────────────────────────┐
│ 🎉 服务完成提醒                      │
├─────────────────────────────────────┤
│ 所有服务项目都已完成，               │
│ 是否完成整体服务？                   │
│                                     │
│ [稍后完成] [完成服务]                │
└─────────────────────────────────────┘
```

#### 5.2 选择操作
- **[完成服务]**：立即完成整体服务，返回订单列表
- **[稍后完成]**：继续停留在当前页面，可以稍后手动完成

---

## 🔍 查看服务进度

### 方法一：订单详情页面查看
```
订单详情 → 服务时长统计模块 → 查看各服务状态和用时
```

### 方法二：专门的服务时长页面
```
我的页面 → 服务时长 → 查看所有进行中的服务
```

#### 服务时长页面功能
- 📊 显示所有进行中的服务
- ⏱️ 实时更新用时
- 🔄 下拉刷新数据
- 🎯 点击卡片查看订单详情
- ⏹️ 直接结束服务计时

---

## 💡 状态说明

### 服务状态指示器
| 状态 | 颜色 | 说明 | 可用操作 |
|------|------|------|----------|
| 未开始 | 灰色 | 服务尚未开始 | [开始] |
| 进行中 | 蓝色 | 服务正在进行 | [完成] |
| 已完成 | 绿色 | 服务已完成 | 无 |

### 按钮说明
| 按钮 | 颜色 | 功能 |
|------|------|------|
| [开始] | 蓝色 | 开始服务计时 |
| [完成] | 绿色 | 结束服务计时 |

### 时间显示格式
- **开始时间**：`2025-07-01 11:27:18`
- **进行中用时**：`已用时：15分钟`
- **完成用时**：`用时：15分钟`

---

## ⚠️ 重要注意事项

### 操作限制
1. ❌ 只有"服务中"状态的订单才能进行时长统计
2. ❌ 增项服务必须是"已确认"状态才能开始
3. ❌ 每个服务只能有一条活跃的计时记录
4. ❌ 已完成的服务无法重新开始

### 数据安全
1. ✅ 所有操作实时同步到服务器
2. ✅ 页面刷新不会丢失数据
3. ✅ 网络异常时自动重试
4. ✅ 支持离线操作，联网后自动同步

### 最佳实践
1. 🎯 及时点击"完成"按钮，确保时长准确
2. 🎯 保持网络连接稳定
3. 🎯 避免频繁切换应用
4. 🎯 服务完成后立即操作，避免遗忘

---

## 🆘 常见问题解决

### Q1：为什么看不到"开始"按钮？
**可能原因：**
- 订单状态不是"服务中"
- 该服务已经开始过（显示"完成"按钮）
- 增项服务状态不是"已确认"

**解决方法：**
1. 检查订单状态
2. 确认服务是否已开始
3. 联系客服确认增项服务状态

### Q2：点击按钮没有反应
**可能原因：**
- 网络连接异常
- 服务器响应超时
- 数据正在同步中

**解决方法：**
1. 检查网络连接
2. 稍等片刻后重试
3. 重新进入页面

### Q3：时长显示不准确
**可能原因：**
- 设备时间不准确
- 网络延迟
- 频繁切换应用

**解决方法：**
1. 校准设备时间
2. 保持应用在前台
3. 确保网络稳定

### Q4：如何查看历史记录？
**查看方法：**
1. 进入订单详情页面
2. 查看"服务时长统计"模块
3. 所有已完成的服务都会显示总用时

---

## 📞 技术支持

如遇到无法解决的问题，请联系技术支持：

📧 **邮箱**：<EMAIL>  
📱 **电话**：400-xxx-xxxx  
🕐 **服务时间**：9:00-18:00（工作日）

---

## 🎬 操作演示

### 完整操作流程动图说明

#### 场景一：标准服务流程
```
1. 订单列表 → 点击"开始服务" → 主服务自动开始计时
2. 订单详情 → 服务时长统计 → 查看进度
3. 点击"完成" → 确认完成 → 服务结束
4. 系统提醒 → 完成整体服务 → 返回列表
```

#### 场景二：有增项服务的流程
```
1. 主服务自动开始 → 进行中状态
2. 增项服务手动开始 → 点击"开始"按钮
3. 主服务完成 → 点击"完成"按钮
4. 增项服务完成 → 点击"完成"按钮
5. 系统自动提醒 → 完成整体服务
```

---

## 📊 数据统计说明

### 服务时长汇总
在订单详情页面底部，可以看到服务时长汇总信息：

```
┌─────────────────────────────────────┐
│ 📊 服务时长汇总                      │
├─────────────────────────────────────┤
│ 总项目数：3个                        │
│ 已完成：2个                          │
│ 进行中：1个                          │
│ 总用时：45分钟                       │
└─────────────────────────────────────┘
```

### 统计项目说明
- **总项目数**：主服务 + 已确认的增项服务数量
- **已完成**：已结束计时的服务数量
- **进行中**：正在计时的服务数量
- **总用时**：所有已完成服务的时长总和

---

## 🔧 高级功能

### 批量操作
在服务时长页面（我的 → 服务时长），支持：
- 查看所有进行中的服务
- 一键结束多个服务
- 批量查看服务详情

### 智能提醒
系统会在以下情况自动提醒：
1. 🔔 服务时长超过预期时间
2. 🔔 所有服务完成时
3. 🔔 长时间未操作时
4. 🔔 网络异常恢复时

### 数据导出
管理员可以导出服务时长数据：
- 按员工导出
- 按时间段导出
- 按服务类型导出
- Excel格式支持

---

## 🎯 效率提升技巧

### 快速操作技巧
1. **双击快速完成**：在服务时长页面双击卡片快速完成服务
2. **下拉刷新**：在任何页面下拉刷新获取最新数据
3. **长按查看详情**：长按服务卡片查看详细信息
4. **手势导航**：左滑返回上一页面

### 时间管理建议
1. 📅 **合理安排**：根据服务时长合理安排工作计划
2. ⏰ **及时记录**：服务开始和结束时及时操作
3. 📈 **数据分析**：定期查看服务效率数据
4. 🎯 **持续改进**：根据数据优化服务流程

---

## 🛠️ 故障排除

### 网络问题
**症状**：按钮点击无反应，数据不更新
**解决步骤**：
1. 检查网络连接状态
2. 尝试切换网络（WiFi ↔ 4G）
3. 重启应用
4. 联系技术支持

### 数据同步问题
**症状**：时长显示不一致，状态错误
**解决步骤**：
1. 下拉刷新页面
2. 退出重新进入页面
3. 检查服务器状态
4. 清除应用缓存

### 按钮状态异常
**症状**：应该显示"开始"但显示"完成"
**解决步骤**：
1. 确认服务实际状态
2. 刷新页面数据
3. 检查网络同步
4. 联系技术支持确认

---

## 📋 检查清单

### 开始服务前
- [ ] 确认订单状态为"待服务"
- [ ] 检查网络连接正常
- [ ] 确认设备时间准确
- [ ] 了解服务项目内容

### 服务进行中
- [ ] 定期检查计时状态
- [ ] 保持应用在前台运行
- [ ] 及时处理增项服务
- [ ] 注意系统提醒消息

### 服务完成后
- [ ] 确认所有服务都已完成
- [ ] 检查时长记录准确性
- [ ] 完成整体服务操作
- [ ] 查看服务时长汇总

---

## 🏆 最佳实践案例

### 案例一：高效洗护服务
**场景**：小体猫洗护 + 美容护理增项
**操作流程**：
1. 开始服务 → 主服务自动计时
2. 执行洗护服务 → 实时监控用时
3. 洗护完成 → 立即点击"完成"
4. 开始增项服务 → 点击"开始"按钮
5. 增项完成 → 点击"完成"按钮
6. 系统提醒 → 完成整体服务
**结果**：总用时45分钟，客户满意度高

### 案例二：多项目并行服务
**场景**：多个增项服务同时进行
**操作技巧**：
1. 合理安排服务顺序
2. 利用等待时间处理其他项目
3. 及时记录每个项目的完成时间
4. 使用服务时长页面统一管理
**效果**：提高服务效率30%

---

*📝 使用指南版本：v1.0.0*
*📅 最后更新：2025-07-01*
*👥 适用对象：员工端用户*
*🔄 更新频率：根据功能迭代及时更新*
