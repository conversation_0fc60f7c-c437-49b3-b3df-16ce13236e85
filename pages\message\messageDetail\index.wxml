<view class="container message-detail-container">
  <view class="content">
    <view wx:if="{{loading}}" class="loading-container">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{messageDetail}}" class="message-detail">
      <!-- 消息头部 -->
      <view class="message-header {{messageDetail.isRead ? 'read' : 'unread'}}">
        <view class="message-icon-wrapper">
          <image src="{{messageDetail.icon}}" class="message-icon" mode="widthFix"></image>
          <view wx:if="{{!messageDetail.isRead}}" class="unread-dot"></view>
        </view>
        <view class="message-info">
          <text class="message-title {{messageDetail.isRead ? 'read-title' : 'unread-title'}}">{{messageDetail.title}}</text>
          <text class="message-time">{{messageDetail.time}}</text>
          <text wx:if="{{messageDetail.isRead && messageDetail.readAt}}" class="read-status">已读</text>
        </view>
      </view>

      <!-- 消息内容 -->
      <view class="message-content">
        <text class="content-text">{{messageDetail.content}}</text>
      </view>

      <!-- 扩展信息 -->
      <view wx:if="{{messageDetail.parsedExtraData}}" class="message-extra">
        <text class="extra-title">相关信息：</text>
        <view class="extra-list">
          <view wx:for="{{messageDetail.parsedExtraData}}" wx:key="label" class="extra-item">
            <text class="extra-label">{{item.label}}：</text>
            <text class="extra-value">{{item.value}}</text>
          </view>
        </view>
      </view>
    </view>

    <view wx:else class="error-container">
      <text>消息不存在或已被删除</text>
    </view>
  </view>
</view>