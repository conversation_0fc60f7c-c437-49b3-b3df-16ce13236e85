Component({
  properties: {
    confirmedAdditionalServiceOrders: {
      type: Array,
      value: []
    }
  },

  data: {},

  methods: {
    // 确认追加服务（如果有待确认状态）
    onConfirmService(e) {
      const service = e.currentTarget.dataset.service;
      this.triggerEvent('confirmService', { service });
    },

    // 拒绝追加服务（如果有待确认状态）
    onRejectService(e) {
      const service = e.currentTarget.dataset.service;
      this.triggerEvent('rejectService', { service });
    },

    // 删除追加服务（仅未付款的可删除）
    onDeleteService(e) {
      const service = e.currentTarget.dataset.service;
      this.triggerEvent('deleteService', { service });
    },

    // 检查是否可以删除追加服务
    canDeleteService(service) {
      // 只有未付款的追加服务可以删除
      return service.status === 'pending_payment' || service.status === 'confirmed';
    }
  }
});
