Component({
  properties: {
    // 模块标题
    title: {
      type: String,
      value: ''
    },
    // 是否有内容
    hasContent: {
      type: Boolean,
      value: false
    },
    // 是否正在加载
    loading: {
      type: Boolean,
      value: false
    },
    // 空状态提示文字
    emptyText: {
      type: String,
      value: '暂无相关内容'
    },
    // 空状态图标
    emptyIcon: {
      type: String,
      value: ''
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: false
    },
    // 操作按钮文字
    actionText: {
      type: String,
      value: '添加'
    },
    // 模块类型（用于样式区分）
    moduleType: {
      type: String,
      value: 'default'
    }
  },

  data: {},

  methods: {
    // 操作按钮点击事件
    onActionClick() {
      this.triggerEvent('action');
    }
  }
});
