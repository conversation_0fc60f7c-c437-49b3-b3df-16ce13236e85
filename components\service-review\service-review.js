Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    reviewData: {
      type: Object,
      value: null
    },
    hasReview: {
      type: Boolean,
      value: false
    },
    reviewLoading: {
      type: Boolean,
      value: false
    }
  },

  data: {},

  methods: {
    // 预览评价图片
    previewImage(e) {
      const { current, urls } = e.currentTarget.dataset;
      wx.previewImage({
        current: current,
        urls: urls,
      });
    },

    // 查看评价详情
    onViewReview() {
      this.triggerEvent('viewReview');
    }
  }
});
