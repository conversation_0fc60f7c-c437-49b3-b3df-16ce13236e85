/* pages/mine/promotion/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 24rpx;
  padding-bottom: 160rpx;
}

/* 卡片通用样式 */
.promotion-card,
.statistics-card,
.customers-card,
.info-card {
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 推广码卡片 */
.promotion-code-section {
  text-align: center;
}

.code-display {
  background: linear-gradient(135deg, #3083FF 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.code-display.no-code {
  justify-content: center;
}

.code-text {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  letter-spacing: 4rpx;
}

.code-text.placeholder {
  opacity: 0.7;
  letter-spacing: 2rpx;
  text-align: center;
}

.copy-icon {
  font-size: 32rpx;
  margin-left: 20rpx;
  opacity: 0.9;
  color: white;
}



/* 无推广码提示 */
.no-code-tip {
  text-align: center;
  padding: 30rpx 0;
}

.tip-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 统计卡片 */
.stats-grid {
  display: flex;
  align-items: center;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2f83ff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.stat-divider {
  width: 2rpx;
  height: 80rpx;
  background-color: #eee;
  margin: 0 30rpx;
}

/* 推广用户卡片 */
.view-all {
  display: flex;
  align-items: center;
  color: #2f83ff;
}

.view-all-text {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.arrow-icon {
  font-size: 24rpx;
}

.customers-preview {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
}

.preview-text {
  font-size: 28rpx;
}

/* 推广说明卡片 */
.info-content {
  padding-left: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: #2f83ff;
  color: white;
  border-radius: 50%;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #2f83ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
}
