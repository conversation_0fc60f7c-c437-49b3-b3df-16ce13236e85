.address-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.address-editor.show {
  opacity: 1;
  visibility: visible;
}

.editor-content {
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.address-editor.show .editor-content {
  transform: translateY(0);
}

/* 标题栏 */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 50%;
}

/* 表单内容 */
.editor-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #ff4757;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007aff;
  outline: none;
}

.char-count {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}

.address-input-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.address-input {
  flex: 1;
}

.search-btn {
  width: 120rpx;
  height: 64rpx;
  background-color: rgba(47, 131, 255, 1);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-shadow: 0px 2px 4px rgba(47, 131, 255, 0.3);
}

.search-btn::after {
  border: none;
}

.search-btn:active {
  background-color: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}

/* 位置选择区域 */
.location-section {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.location-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.location-btn {
  flex: 1;
  height: 64rpx;
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 40rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0 20rpx;
}

.location-btn::after {
  border: none;
}

.location-btn.primary {
  background-color: rgba(47, 131, 255, 1);
  color: white;
  border: none;
  box-shadow: 0px 2px 4px rgba(47, 131, 255, 0.3);
}

.location-btn.primary:active {
  background-color: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}

.location-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.location-btn.loading {
  background-color: #e9ecef;
  color: #666;
  opacity: 0.7;
}

.location-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-text {
  font-size: 12px;
  color: #666;
}

/* 操作按钮 */
.editor-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  line-height: 1;
  padding: 0 20rpx;
}

.cancel-btn::after,
.confirm-btn::after {
  border: none;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.cancel-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.confirm-btn {
  background-color: rgba(47, 131, 255, 1);
  color: #fff;
  box-shadow: 0px 2px 4px rgba(47, 131, 255, 0.3);
}

.confirm-btn:active {
  background-color: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}
