<view class="container">
  <view class="content">
    <!-- 订单基本信息卡片 -->
    <view class="order-info-card">
      <view class="order-header">
        <image class="service-image" src="{{orderInfo.productImage}}" mode="aspectFill"></image>
        <view class="service-info">
          <view class="service-name">{{orderInfo.productName}}</view>
          <view class="service-extra" wx:if="{{orderInfo.extraServive && orderInfo.extraServive.length > 0}}">
            增项服务：<text wx:for="{{orderInfo.extraServive}}" wx:for-item="val" wx:key="val">{{val}}{{index < orderInfo.extraServive.length-1 ? '、':''}}</text>
          </view>
          <view class="service-extra" wx:else>增项服务：无</view>
        </view>
      </view>

      <view class="order-details">
        <view class="detail-row">
          <text class="detail-label">服务宠物：</text>
          <text class="detail-value">{{orderInfo.petName}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">宠物主人：</text>
          <text class="detail-value">{{orderInfo.customerName}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">服务地址：</text>
          <text class="detail-value">{{orderInfo.serviceAddress}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">服务时间：</text>
          <text class="detail-value">{{orderInfo.expectTime}}</text>
        </view>
        <!-- 用户备注 -->
        <view class="detail-row" wx:if="{{orderInfo.userRemark}}">
          <text class="detail-label">用户备注：</text>
          <text class="detail-value user-remark">{{orderInfo.userRemark}}</text>
        </view>
      </view>
    </view>

    <!-- 服务评分卡片 -->
    <view class="rating-card">
      <view class="rating-header">
        <text class="rating-label">服务评分</text>
        <view class="rating-score">
          <view class="stars">
            <text wx:for="{{reviewDetail.ratingStars}}" wx:key="index"
                  class="star {{item.type === 'full' ? 'star-full' : item.type === 'half' ? 'star-half' : 'star-empty'}}">★</text>
          </view>
          <text class="score-text">{{reviewDetail.rating || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 评价内容卡片 -->
    <view class="comment-card" wx:if="{{reviewDetail.comment}}">
      <view class="comment-text">{{reviewDetail.comment}}</view>
    </view>

    <!-- 评价图片卡片 -->
    <view class="images-card" wx:if="{{reviewDetail.photoURLs && reviewDetail.photoURLs.length > 0}}">
      <view class="image-item" wx:for="{{reviewDetail.photoURLs}}" wx:key="index">
        <image src="{{item}}" class="review-image" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" data-urls="{{reviewDetail.photoURLs}}"></image>
      </view>
    </view>

    <!-- 评价时间 -->
    <view class="time-info" wx:if="{{reviewDetail.createdAt}}">
      <text class="time-text">评价时间：{{reviewDetail.createdAt}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!reviewDetail.rating && !reviewDetail.comment}}">
      <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
      <text class="empty-text">暂无评价信息</text>
    </view>
  </view>
</view>
