/* pages/complaint/list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 标签栏 */
.tabs-container {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs {
  display: flex;
  padding: 0 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}

.tab-item.active .tab-text {
  color: #ff4f8f;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff4f8f;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 投诉建议列表 */
.complaint-list {
  padding: 20rpx;
}

.complaint-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.complaint-item:active {
  transform: scale(0.98);
}

/* 头部信息 */
.complaint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.category-info {
  display: flex;
  align-items: center;
}

.category-tag {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: #fff;
  margin-right: 16rpx;
}

.category-tag.complaint {
  background-color: #ff4757;
}

.category-tag.suggestion {
  background-color: #5352ed;
}

.subcategory-text {
  font-size: 24rpx;
  color: #666;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 24rpx;
  font-weight: 500;
}

/* 内容区域 */
.complaint-content {
  margin-bottom: 20rpx;
}

.complaint-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

.complaint-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 订单信息 */
.order-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.order-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.order-sn {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 底部信息 */
.complaint-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid;
  transition: all 0.3s ease;
}

.action-btn.edit {
  color: #007aff;
  border-color: #007aff;
}

.action-btn.edit:active {
  background-color: #007aff;
  color: #fff;
}

.action-btn.delete {
  color: #ff3b30;
  border-color: #ff3b30;
}

.action-btn.delete:active {
  background-color: #ff3b30;
  color: #fff;
}

/* 处理结果 */
.result-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f0f9ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.result-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.result-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.empty-image {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 40rpx;
}

.create-btn {
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  padding: 0 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(255, 79, 143, 0.3);
}

.create-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 79, 143, 0.3);
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #ff4f8f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 79, 143, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
  padding-bottom: 15rpx;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 68rpx;
  color: #fff;
  font-weight: 300;
}
