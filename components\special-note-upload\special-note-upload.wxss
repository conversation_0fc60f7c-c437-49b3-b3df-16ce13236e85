/* 遮罩层 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

/* 弹窗容器 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

/* 弹窗内容 */
.modal-content {
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.modal-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20px;
}

/* 弹窗主体 */
.modal-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 文字输入区域 */
.content-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.content-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 24rpx;
  line-height: 1.5;
  box-sizing: border-box;
  background-color: #fff;
}

.content-textarea[disabled] {
  background-color: #f5f5f5;
  color: #999;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 图片上传区域 */
.photo-section {
  margin-bottom: 20px;
}

.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.photo-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.photo-upload {
  width: 80px;
  height: 80px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-image {
  width: 100%;
  height: 100%;
}

/* 只读模式样式 */
.readonly .content-textarea {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
}

.readonly .photo-delete {
  display: none;
}

.readonly .photo-upload {
  display: none;
}

/* 弹窗底部 */
.modal-footer {
  padding: 40rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.footer-btn {
  flex: 1;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.footer-btn::after {
  border: none;
}

.cancel-btn {
  background-color: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
}

.cancel-btn:active {
  background-color: rgba(220, 220, 220, 1);
  transform: scale(0.95);
}

.confirm-btn {
  background-color: rgba(47, 131, 255, 1);
  color: white;
}

.confirm-btn:active {
  background-color: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}

.delete-btn {
  background-color: rgba(255, 67, 145, 1);
  color: white;
}

.delete-btn:active {
  background-color: rgba(255, 67, 145, 0.8);
  transform: scale(0.95);
}

.readonly .confirm-btn {
  background-color: rgba(47, 131, 255, 1);
}

/* 加载状态 */
.uploading {
  opacity: 0.6;
  pointer-events: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 10px;
  color: #999;
  font-size: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.5;
  max-width: 80%;
  height: 120px;
}
