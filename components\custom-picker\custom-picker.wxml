<!--components/custom-picker/index.wxml-->
<view class="time-picker-modal">
  <view class="time-picker-mask" bindtap="onCancel"></view>
  <view class="time-picker-container {{hasBottomNavigation ? '' : 'button-fixed'}}">
    <view class="time-picker-header">
      <text class="cancel-btn" bindtap="onCancel">取消</text>
      <text class="title">选择上门时间</text>
      <text class="confirm-btn" bindtap="onConfirm">确定</text>
    </view>
    <view class="picker-content">
      <view class="picker-label">
        期待上门时间：{{selectedTime || '请选择时间'}}
      </view>
      <view class="time-description">
        可选时间范围：早上7:00 - 晚上10:00
      </view>
      <view class="picker-inner">
        <view class="picker-column">
          <text>年</text>
        </view>
        <view class="picker-column">
          <text>月</text>
        </view>
        <view class="picker-column">
          <text>日</text>
        </view>
        <view class="picker-column">
          <text>时间</text>
        </view>
      </view>
      <picker-view
        class="time-picker-view"
        indicator-style="height: 80rpx;"
        value="{{timeArray}}"
        bindchange="bindTimeChange"
      >
        <picker-view-column>
          <view wx:for="{{timeRange[0]}}" wx:key="index" class="picker-item">{{item}}年</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[1]}}" wx:key="index" class="picker-item">{{item}}月</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[2]}}" wx:key="index" class="picker-item">{{item}}日</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[3]}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>