.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #3083FF 0%, #764ba2 100%);
  padding: 40rpx 30rpx 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-icon {
  margin-bottom: 20rpx;
}

.payment-icon {
  width: 120rpx;
  height: 120rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 服务信息卡片 */
.service-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.service-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.service-status.confirmed {
  background: #4CAF50;
}

.service-status.pending_payment {
  background: #FF5722;
}

/* 订单信息 */
.order-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.info-phone {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 服务描述 */
.service-description {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.description-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.description-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 价格卡片 */
.price-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.price-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

.total-row {
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
  margin-top: 10rpx;
}

.total-row .price-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.total-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4391;
}

/* 支付方式 */
.payment-method {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.method-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  position: relative;
}

.method-item.selected {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.method-name {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.method-check {
  width: 32rpx;
  height: 32rpx;
}

.check-icon {
  width: 100%;
  height: 100%;
}

/* 支付按钮 */
.payment-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.detail-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  background: #fff;
  color: #3083FF;
  border: 2rpx solid #3083FF;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.pay-btn {
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #3083FF 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.pay-btn.loading {
  opacity: 0.7;
}

.pay-btn::after {
  border: none;
}

/* 温馨提示 */
.tips {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tips-content text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
