<!-- pages/mine/promotion/customers/index.wxml -->
<wxs module="utils">
function formatTime(timestamp) {
  if (!timestamp) return '';

  var date = getDate(timestamp);
  var now = getDate();
  var diff = now.getTime() - date.getTime();

  if (diff < 60000) {
    return '刚刚';
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前';
  } else if (diff < 604800000) {
    return Math.floor(diff / 86400000) + '天前';
  } else {
    return date.toLocaleDateString();
  }
}

function getMemberStatusText(status) {
  var statusMap = {};
  statusMap[0] = '普通用户';
  statusMap[1] = '会员用户';
  statusMap[2] = 'VIP用户';
  return statusMap[status] || '未知状态';
}

function getMemberStatusColor(status) {
  var colorMap = {};
  colorMap[0] = '#999';
  colorMap[1] = '#2f83ff';
  colorMap[2] = '#ff6b35';
  return colorMap[status] || '#999';
}

module.exports = {
  formatTime: formatTime,
  getMemberStatusText: getMemberStatusText,
  getMemberStatusColor: getMemberStatusColor
};
</wxs>

<view class="container">
  <!-- 统计信息 -->
  <view class="stats-header">
    <text class="stats-title">推广统计</text>
    <text class="stats-count">共推广 {{total}} 位用户</text>
  </view>

  <!-- 客户列表 -->
  <view class="customer-list">
    <view
      wx:for="{{customerList}}"
      wx:key="id"
      class="customer-item"
    >
      <!-- 用户头像和基本信息 -->
      <view class="customer-info">
        <image 
          class="customer-avatar" 
          src="{{item.avatar || '//xian7.zos.ctyun.cn/pet/static/memberAvatar.png'}}" 
          mode="aspectFill"
        ></image>
        
        <view class="customer-details">
          <view class="customer-name-row">
            <text class="customer-name">{{item.nickname || '用户' + item.id}}</text>
            <text 
              class="member-status" 
              style="color: {{utils.getMemberStatusColor(item.memberStatus)}}"
            >{{utils.getMemberStatusText(item.memberStatus)}}</text>
          </view>
          
          <view class="customer-meta">
            <text class="customer-phone">{{item.phone}}</text>
            <text class="join-time">{{utils.formatTime(item.createdAt)}}</text>
          </view>
          
          <view class="customer-points" wx:if="{{item.points > 0}}">
            <text class="points-label">积分：</text>
            <text class="points-value">{{item.points}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{customerList.length === 0 && !loading}}">
      <image class="empty-image" src="//xian7.zos.ctyun.cn/pet/static/zwtgyh.png" mode="aspectFit"></image>
      <text class="empty-desc">快去分享您的推广码吧！</text>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && customerList.length > 0}}">
      <text>没有更多了</text>
    </view>
  </view>
</view>
