import request, { analysisRes } from "../request";
import config from "../config";

const { employeeCheckin } = config.apiUrls;

export default {
  // 创建打卡记录
  async create(data) {
    console.log('API调用 - 创建打卡记录:', employeeCheckin.create, data);
    const res = await request.post(employeeCheckin.create, data);
    console.log('API响应 - 创建打卡记录:', res);
    const result = analysisRes(res);
    return result;
  },

  // 获取员工打卡记录列表
  async getList(employeeId, params = {}) {
    const url = employeeCheckin.list.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取今日打卡记录
  async getTodayList(employeeId) {
    const url = employeeCheckin.todayList.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取打卡统计信息
  async getStatistics(employeeId, params = {}) {
    const url = employeeCheckin.statistics.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 删除打卡记录
  async delete(id) {
    const url = employeeCheckin.delete.replace('{id}', id);
    const res = await request.post(url, {});
    const result = analysisRes(res);
    return result;
  },

  // 检查员工最后打卡时间
  async getLastCheckinTime(employeeId) {
    if (!employeeId) {
      console.log('员工ID为空，跳过获取最后打卡时间');
      return null;
    }

    try {
      // 使用专门的API接口
      const url = employeeCheckin.lastCheckinTime.replace('{employeeId}', employeeId);
      const res = await request.get(url, {
        hideLoading: true,
      });

      // 手动处理响应，避免显示错误提示框
      const { errCode, msg, data } = res || {};
      if (errCode !== 0) {
        console.log('获取最后打卡时间失败，但不显示提示框:', msg);
        // 如果专门的API不可用，回退到使用列表API
        try {
          const listRes = await this.getList(employeeId, {
            current: 1,
            pageSize: 1,
            hideLoading: true,
          });

          if (listRes && listRes.list && listRes.list.length > 0) {
            return listRes.list[0].checkInTime;
          }
          return null;
        } catch (fallbackError) {
          console.error('获取最后打卡时间失败:', fallbackError);
          return null;
        }
      }

      // 根据API响应结构解析数据
      if (data && data.lastCheckInTime) {
        console.log(`获取到最后打卡时间: ${data.lastCheckInTime}`);
        return data.lastCheckInTime;
      } else {
        console.log('员工从未打卡，lastCheckInTime为null');
        return null;
      }
    } catch (error) {
      console.log('专门API不可用，使用列表API获取最后打卡时间');
      // 如果专门的API不可用，回退到使用列表API
      try {
        const res = await this.getList(employeeId, {
          current: 1,
          pageSize: 1,
          hideLoading: true,
        });

        if (res && res.list && res.list.length > 0) {
          return res.list[0].checkInTime;
        }
        return null;
      } catch (fallbackError) {
        console.error('获取最后打卡时间失败:', fallbackError);
        return null;
      }
    }
  },

  // 获取员工最后打卡信息（包含更多统计数据）
  async getLastCheckinInfo(employeeId) {
    if (!employeeId) {
      console.log('员工ID为空，跳过获取最后打卡信息');
      return null;
    }

    try {
      const url = employeeCheckin.lastCheckinTime.replace('{employeeId}', employeeId);
      const res = await request.get(url, {
        hideLoading: true,
      });

      // 手动处理响应，避免显示错误提示框
      const { errCode, msg, data } = res || {};
      if (errCode !== 0) {
        console.log('获取最后打卡信息失败，但不显示提示框:', msg);
        return null;
      }

      if (data) {
        console.log('获取到最后打卡信息:', data);
        return {
          employeeId: data.employeeId,
          lastCheckInTime: data.lastCheckInTime,
          lastCheckInId: data.lastCheckInId,
          totalCheckInCount: data.totalCheckInCount || 0,
          todayCheckInCount: data.todayCheckInCount || 0
        };
      }
      return null;
    } catch (error) {
      console.error('获取最后打卡信息失败:', error);
      return null;
    }
  },

  // 检查是否需要强制打卡（超过一周未打卡）
  async checkNeedForceCheckin(employeeId) {
    if (!employeeId) {
      console.log('员工ID为空，跳过强制打卡检查');
      return false;
    }

    try {
      // 优先使用完整的打卡信息API
      const checkinInfo = await this.getLastCheckinInfo(employeeId);

      if (!checkinInfo || !checkinInfo.lastCheckInTime) {
        // 从未打卡，需要强制打卡
        console.log('员工从未打卡，需要强制打卡');
        return true;
      }

      const lastCheckin = new Date(checkinInfo.lastCheckInTime);
      const now = new Date();
      const diffTime = now.getTime() - lastCheckin.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);

      console.log(`距离上次打卡已过去 ${diffDays.toFixed(1)} 天`);
      console.log(`总打卡次数: ${checkinInfo.totalCheckInCount}, 今日打卡次数: ${checkinInfo.todayCheckInCount}`);

      // 超过7天未打卡，需要强制打卡
      const needForceCheckin = diffDays > 7;
      if (needForceCheckin) {
        console.log('超过7天未打卡，需要强制打卡');
      }

      return needForceCheckin;
    } catch (error) {
      console.error('检查打卡状态失败:', error);
      // 出错时不强制打卡，避免影响正常使用
      return false;
    }
  },
};
